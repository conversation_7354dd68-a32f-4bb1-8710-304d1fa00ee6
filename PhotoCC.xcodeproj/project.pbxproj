// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0E43A62E2E26552A00691075 /* PhotoCollectionViewItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6232E26552A00691075 /* PhotoCollectionViewItem.swift */; };
		0E43A62F2E26552A00691075 /* PhotoItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6242E26552A00691075 /* PhotoItem.swift */; };
		0E43A6302E26552A00691075 /* SessionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6292E26552A00691075 /* SessionViewController.swift */; };
		0E43A6312E26552A00691075 /* SessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6282E26552A00691075 /* SessionManager.swift */; };
		0E43A6322E26552A00691075 /* SidebarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A62A2E26552A00691075 /* SidebarViewController.swift */; };
		0E43A6332E26552A00691075 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A61C2E26552A00691075 /* AppDelegate.swift */; };
		0E43A6342E26552A00691075 /* MainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6202E26552A00691075 /* MainViewController.swift */; };
		0E43A6352E26552A00691075 /* MainWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6212E26552A00691075 /* MainWindowController.swift */; };
		0E43A6362E26552A00691075 /* SessionCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6272E26552A00691075 /* SessionCellView.swift */; };
		0E43A6372E26552A00691075 /* ThumbnailGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A62B2E26552A00691075 /* ThumbnailGenerator.swift */; };
		0E43A6382E26552A00691075 /* ThumbnailPerformanceMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A62C2E26552A00691075 /* ThumbnailPerformanceMonitor.swift */; };
		0E43A6392E26552A00691075 /* PhotoLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6252E26552A00691075 /* PhotoLoader.swift */; };
		0E43A63A2E26552A00691075 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E43A6262E26552A00691075 /* Session.swift */; };
		0E43A63B2E26552A00691075 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0E43A61F2E26552A00691075 /* MainMenu.xib */; };
		0E43A63C2E26552A00691075 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0E43A61D2E26552A00691075 /* Assets.xcassets */; };
		1A8027475D6DF2FEF7445F61 /* Pods_PhotoCC_PhotoCCUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1FE38A0EE94A5F94C5BF8D8A /* Pods_PhotoCC_PhotoCCUITests.framework */; };
		5CC6B68E3B1FB99C21E2495F /* Pods_PhotoCCTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 241D24FD4B4E8118279928CB /* Pods_PhotoCCTests.framework */; };
		5FBD9AAE0AE6232FA38FDF8D /* Pods_PhotoCC.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9672C8BE39BF05F239AD76B /* Pods_PhotoCC.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0E248E672E26447D001AFB2A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0E248E4F2E26447B001AFB2A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0E248E562E26447B001AFB2A;
			remoteInfo = PhotoCC;
		};
		0E248E712E26447D001AFB2A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0E248E4F2E26447B001AFB2A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0E248E562E26447B001AFB2A;
			remoteInfo = PhotoCC;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0E248E572E26447B001AFB2A /* PhotoCC.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PhotoCC.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0E248E662E26447D001AFB2A /* PhotoCCTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoCCTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0E248E702E26447D001AFB2A /* PhotoCCUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoCCUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0E43A61C2E26552A00691075 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		0E43A61D2E26552A00691075 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0E43A61E2E26552A00691075 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/MainMenu.xib; sourceTree = "<group>"; };
		0E43A6202E26552A00691075 /* MainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewController.swift; sourceTree = "<group>"; };
		0E43A6212E26552A00691075 /* MainWindowController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainWindowController.swift; sourceTree = "<group>"; };
		0E43A6222E26552A00691075 /* PhotoCC.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = PhotoCC.entitlements; sourceTree = "<group>"; };
		0E43A6232E26552A00691075 /* PhotoCollectionViewItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoCollectionViewItem.swift; sourceTree = "<group>"; };
		0E43A6242E26552A00691075 /* PhotoItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoItem.swift; sourceTree = "<group>"; };
		0E43A6252E26552A00691075 /* PhotoLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoLoader.swift; sourceTree = "<group>"; };
		0E43A6262E26552A00691075 /* Session.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Session.swift; sourceTree = "<group>"; };
		0E43A6272E26552A00691075 /* SessionCellView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SessionCellView.swift; sourceTree = "<group>"; };
		0E43A6282E26552A00691075 /* SessionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SessionManager.swift; sourceTree = "<group>"; };
		0E43A6292E26552A00691075 /* SessionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SessionViewController.swift; sourceTree = "<group>"; };
		0E43A62A2E26552A00691075 /* SidebarViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SidebarViewController.swift; sourceTree = "<group>"; };
		0E43A62B2E26552A00691075 /* ThumbnailGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThumbnailGenerator.swift; sourceTree = "<group>"; };
		0E43A62C2E26552A00691075 /* ThumbnailPerformanceMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThumbnailPerformanceMonitor.swift; sourceTree = "<group>"; };
		1FE38A0EE94A5F94C5BF8D8A /* Pods_PhotoCC_PhotoCCUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCC_PhotoCCUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		241D24FD4B4E8118279928CB /* Pods_PhotoCCTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCCTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		26CA89017BC680C1B53AD1B1 /* Pods-PhotoCC.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCC.release.xcconfig"; path = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC.release.xcconfig"; sourceTree = "<group>"; };
		96908BDF04E43162A94CBEBC /* Pods-PhotoCCTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCCTests.debug.xcconfig"; path = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests.debug.xcconfig"; sourceTree = "<group>"; };
		C2DA57E7E49334D94A8D9D81 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCC-PhotoCCUITests.debug.xcconfig"; path = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests.debug.xcconfig"; sourceTree = "<group>"; };
		C3D2745DC0ACFC32CDADA1D4 /* Pods-PhotoCC.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCC.debug.xcconfig"; path = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC.debug.xcconfig"; sourceTree = "<group>"; };
		DE3F01E575EEB2A5FF1FA512 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCC-PhotoCCUITests.release.xcconfig"; path = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests.release.xcconfig"; sourceTree = "<group>"; };
		F7DB211190ADC160F2022EDA /* Pods-PhotoCCTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhotoCCTests.release.xcconfig"; path = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests.release.xcconfig"; sourceTree = "<group>"; };
		F9672C8BE39BF05F239AD76B /* Pods_PhotoCC.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCC.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		0E248E692E26447D001AFB2A /* PhotoCCTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoCCTests;
			sourceTree = "<group>";
		};
		0E248E732E26447D001AFB2A /* PhotoCCUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoCCUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		0E248E542E26447B001AFB2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5FBD9AAE0AE6232FA38FDF8D /* Pods_PhotoCC.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E632E26447D001AFB2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5CC6B68E3B1FB99C21E2495F /* Pods_PhotoCCTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E6D2E26447D001AFB2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A8027475D6DF2FEF7445F61 /* Pods_PhotoCC_PhotoCCUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E248E4E2E26447B001AFB2A = {
			isa = PBXGroup;
			children = (
				0E43A62D2E26552A00691075 /* PhotoCC */,
				0E248E692E26447D001AFB2A /* PhotoCCTests */,
				0E248E732E26447D001AFB2A /* PhotoCCUITests */,
				0E248E582E26447B001AFB2A /* Products */,
				FA5A85E128C3C5A7746954BA /* Pods */,
				870FEF09DB273DB49E4198BA /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		0E248E582E26447B001AFB2A /* Products */ = {
			isa = PBXGroup;
			children = (
				0E248E572E26447B001AFB2A /* PhotoCC.app */,
				0E248E662E26447D001AFB2A /* PhotoCCTests.xctest */,
				0E248E702E26447D001AFB2A /* PhotoCCUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0E43A62D2E26552A00691075 /* PhotoCC */ = {
			isa = PBXGroup;
			children = (
				0E43A61C2E26552A00691075 /* AppDelegate.swift */,
				0E43A61D2E26552A00691075 /* Assets.xcassets */,
				0E43A61F2E26552A00691075 /* MainMenu.xib */,
				0E43A6202E26552A00691075 /* MainViewController.swift */,
				0E43A6212E26552A00691075 /* MainWindowController.swift */,
				0E43A6222E26552A00691075 /* PhotoCC.entitlements */,
				0E43A6232E26552A00691075 /* PhotoCollectionViewItem.swift */,
				0E43A6242E26552A00691075 /* PhotoItem.swift */,
				0E43A6252E26552A00691075 /* PhotoLoader.swift */,
				0E43A6262E26552A00691075 /* Session.swift */,
				0E43A6272E26552A00691075 /* SessionCellView.swift */,
				0E43A6282E26552A00691075 /* SessionManager.swift */,
				0E43A6292E26552A00691075 /* SessionViewController.swift */,
				0E43A62A2E26552A00691075 /* SidebarViewController.swift */,
				0E43A62B2E26552A00691075 /* ThumbnailGenerator.swift */,
				0E43A62C2E26552A00691075 /* ThumbnailPerformanceMonitor.swift */,
			);
			path = PhotoCC;
			sourceTree = "<group>";
		};
		870FEF09DB273DB49E4198BA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F9672C8BE39BF05F239AD76B /* Pods_PhotoCC.framework */,
				1FE38A0EE94A5F94C5BF8D8A /* Pods_PhotoCC_PhotoCCUITests.framework */,
				241D24FD4B4E8118279928CB /* Pods_PhotoCCTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FA5A85E128C3C5A7746954BA /* Pods */ = {
			isa = PBXGroup;
			children = (
				C3D2745DC0ACFC32CDADA1D4 /* Pods-PhotoCC.debug.xcconfig */,
				26CA89017BC680C1B53AD1B1 /* Pods-PhotoCC.release.xcconfig */,
				C2DA57E7E49334D94A8D9D81 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */,
				DE3F01E575EEB2A5FF1FA512 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */,
				96908BDF04E43162A94CBEBC /* Pods-PhotoCCTests.debug.xcconfig */,
				F7DB211190ADC160F2022EDA /* Pods-PhotoCCTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0E248E562E26447B001AFB2A /* PhotoCC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E248E7A2E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCC" */;
			buildPhases = (
				64435EF5639050E50B93B09F /* [CP] Check Pods Manifest.lock */,
				0E248E532E26447B001AFB2A /* Sources */,
				0E248E542E26447B001AFB2A /* Frameworks */,
				0E248E552E26447B001AFB2A /* Resources */,
				1533F2213AC50305F20F1E0D /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PhotoCC;
			productName = PhotoCC;
			productReference = 0E248E572E26447B001AFB2A /* PhotoCC.app */;
			productType = "com.apple.product-type.application";
		};
		0E248E652E26447D001AFB2A /* PhotoCCTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E248E7D2E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCCTests" */;
			buildPhases = (
				6E36A60D9628F40AA4A40BDC /* [CP] Check Pods Manifest.lock */,
				0E248E622E26447D001AFB2A /* Sources */,
				0E248E632E26447D001AFB2A /* Frameworks */,
				0E248E642E26447D001AFB2A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0E248E682E26447D001AFB2A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				0E248E692E26447D001AFB2A /* PhotoCCTests */,
			);
			name = PhotoCCTests;
			productName = PhotoCCTests;
			productReference = 0E248E662E26447D001AFB2A /* PhotoCCTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		0E248E6F2E26447D001AFB2A /* PhotoCCUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E248E802E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCCUITests" */;
			buildPhases = (
				61B7F8A4A1CB350E1C27792E /* [CP] Check Pods Manifest.lock */,
				0E248E6C2E26447D001AFB2A /* Sources */,
				0E248E6D2E26447D001AFB2A /* Frameworks */,
				0E248E6E2E26447D001AFB2A /* Resources */,
				9917BC8CA6A4C30E55B1D1F4 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0E248E722E26447D001AFB2A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				0E248E732E26447D001AFB2A /* PhotoCCUITests */,
			);
			name = PhotoCCUITests;
			productName = PhotoCCUITests;
			productReference = 0E248E702E26447D001AFB2A /* PhotoCCUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0E248E4F2E26447B001AFB2A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					0E248E562E26447B001AFB2A = {
						CreatedOnToolsVersion = 16.4;
					};
					0E248E652E26447D001AFB2A = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 0E248E562E26447B001AFB2A;
					};
					0E248E6F2E26447D001AFB2A = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 0E248E562E26447B001AFB2A;
					};
				};
			};
			buildConfigurationList = 0E248E522E26447B001AFB2A /* Build configuration list for PBXProject "PhotoCC" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0E248E4E2E26447B001AFB2A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 0E248E582E26447B001AFB2A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0E248E562E26447B001AFB2A /* PhotoCC */,
				0E248E652E26447D001AFB2A /* PhotoCCTests */,
				0E248E6F2E26447D001AFB2A /* PhotoCCUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0E248E552E26447B001AFB2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0E43A63B2E26552A00691075 /* MainMenu.xib in Resources */,
				0E43A63C2E26552A00691075 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E642E26447D001AFB2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E6E2E26447D001AFB2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1533F2213AC50305F20F1E0D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhotoCC/Pods-PhotoCC-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhotoCC/Pods-PhotoCC-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PhotoCC/Pods-PhotoCC-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		61B7F8A4A1CB350E1C27792E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PhotoCC-PhotoCCUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		64435EF5639050E50B93B09F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PhotoCC-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		6E36A60D9628F40AA4A40BDC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PhotoCCTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9917BC8CA6A4C30E55B1D1F4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0E248E532E26447B001AFB2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0E43A62E2E26552A00691075 /* PhotoCollectionViewItem.swift in Sources */,
				0E43A62F2E26552A00691075 /* PhotoItem.swift in Sources */,
				0E43A6302E26552A00691075 /* SessionViewController.swift in Sources */,
				0E43A6312E26552A00691075 /* SessionManager.swift in Sources */,
				0E43A6322E26552A00691075 /* SidebarViewController.swift in Sources */,
				0E43A6332E26552A00691075 /* AppDelegate.swift in Sources */,
				0E43A6342E26552A00691075 /* MainViewController.swift in Sources */,
				0E43A6352E26552A00691075 /* MainWindowController.swift in Sources */,
				0E43A6362E26552A00691075 /* SessionCellView.swift in Sources */,
				0E43A6372E26552A00691075 /* ThumbnailGenerator.swift in Sources */,
				0E43A6382E26552A00691075 /* ThumbnailPerformanceMonitor.swift in Sources */,
				0E43A6392E26552A00691075 /* PhotoLoader.swift in Sources */,
				0E43A63A2E26552A00691075 /* Session.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E622E26447D001AFB2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E248E6C2E26447D001AFB2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0E248E682E26447D001AFB2A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0E248E562E26447B001AFB2A /* PhotoCC */;
			targetProxy = 0E248E672E26447D001AFB2A /* PBXContainerItemProxy */;
		};
		0E248E722E26447D001AFB2A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0E248E562E26447B001AFB2A /* PhotoCC */;
			targetProxy = 0E248E712E26447D001AFB2A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		0E43A61F2E26552A00691075 /* MainMenu.xib */ = {
			isa = PBXVariantGroup;
			children = (
				0E43A61E2E26552A00691075 /* Base */,
			);
			name = MainMenu.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0E248E782E26447D001AFB2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		0E248E792E26447D001AFB2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		0E248E7B2E26447D001AFB2A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C3D2745DC0ACFC32CDADA1D4 /* Pods-PhotoCC.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PhotoCC/PhotoCC.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainNibFile = MainMenu;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		0E248E7C2E26447D001AFB2A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 26CA89017BC680C1B53AD1B1 /* Pods-PhotoCC.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PhotoCC/PhotoCC.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainNibFile = MainMenu;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		0E248E7E2E26447D001AFB2A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 96908BDF04E43162A94CBEBC /* Pods-PhotoCCTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoCC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoCC";
			};
			name = Debug;
		};
		0E248E7F2E26447D001AFB2A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F7DB211190ADC160F2022EDA /* Pods-PhotoCCTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoCC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoCC";
			};
			name = Release;
		};
		0E248E812E26447D001AFB2A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C2DA57E7E49334D94A8D9D81 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCCUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = PhotoCC;
			};
			name = Debug;
		};
		0E248E822E26447D001AFB2A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DE3F01E575EEB2A5FF1FA512 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoCCUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = PhotoCC;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0E248E522E26447B001AFB2A /* Build configuration list for PBXProject "PhotoCC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E248E782E26447D001AFB2A /* Debug */,
				0E248E792E26447D001AFB2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E248E7A2E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E248E7B2E26447D001AFB2A /* Debug */,
				0E248E7C2E26447D001AFB2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E248E7D2E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCCTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E248E7E2E26447D001AFB2A /* Debug */,
				0E248E7F2E26447D001AFB2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E248E802E26447D001AFB2A /* Build configuration list for PBXNativeTarget "PhotoCCUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E248E812E26447D001AFB2A /* Debug */,
				0E248E822E26447D001AFB2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0E248E4F2E26447B001AFB2A /* Project object */;
}
