// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 71;
	objects = {

/* Begin PBXBuildFile section */
		00DAE48C9A4FBCD1FCAA922CA57B45F9 /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = E50F18FFE26A06A3613A9AD987BEDA14 /* SDWebImageDownloaderRequestModifier.m */; };
		042D40751BD2F51FBE9FECD4707CBBE9 /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 030F4C8FEEB02A7EF488C74DEFE32EC3 /* SDDeviceHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		0453019EC6578A67B82CF569EC765546 /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E9A30FE556CB75D6CE9901DC59C4183 /* SDFileAttributeHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		06C4E233E7977DB81A24482E69B2D7D7 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = 339CE8ABB5BCDA4234DF01FEDCE3A1E0 /* UIImage+Transform.m */; };
		089F3C4BAA46A37EC5763DD312771021 /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 02A4E1BF99CF3AC1C42CD5CBD83BFCCE /* SDImageIOAnimatedCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		08D50C5AC969A3701B6F9137CF3A10F1 /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = 2F04D3F6754C97BD9AB72E5D11B251B0 /* UIImage+ForceDecode.m */; };
		09A2ACBC8CE1761652EAA20886AEFE10 /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = B220B76B1B25D9519542C28A3E41A57A /* SDImageCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0B0E6CECDF516BC83756C1D5515A725B /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = E40EE9EF95B7ABCE8A9F458E0287C208 /* SDAsyncBlockOperation.m */; };
		0F1D0F5DCC8C94A4C684DF846D14F436 /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 7EF6A1EA36EF9F455CDFEFA6D9D7D288 /* SDWebImagePrefetcher.m */; };
		0FF9F459ED16719292443A4C99B52B20 /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = B0101691E35DF1C2AF664C7246554E29 /* SDImageCache.m */; };
		10017B43AC38C3A89D7AC1376C6E7066 /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FDFD32CCBD2EC92772F44AFD2765A2B0 /* SDImageLoadersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14CA284AC4FF1EED75E785641EE98034 /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D9C95C7A6F7A628DD1E2C0C59CC20E0 /* SDImageCacheConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		165F1C9CBD621828C788A3018D0426C5 /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = BC9367564F7FAD50D983FA0CD987496A /* SDImageAPNGCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		16D7DCB7CC985C33EEC41B371C029C84 /* SDWebImage.bundle in Resources */ = {isa = PBXBuildFile; fileRef = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage.bundle */; };
		1708C1D28B421C4AD310426D1695CE77 /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = EB1ABFC2E4E3A15C496D6C322D08AD46 /* SDAnimatedImage.m */; };
		1754DD5511A7BF462B116F70B0D4006A /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 726E4A93B81CA122D496C19B3C6A2B07 /* SDWebImageOperation.m */; };
		1830558A4D2D63C8E76BC3136D8213F9 /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = 24D286305B0E83E47229172BF7376DEC /* UIImage+ExtendedCacheData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18660FA595DBE133BB784E813A7122A8 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = AC752F1DC9476A73159A9F931106D070 /* SDImageHEICCoder.m */; };
		18AD90784D549657DF51BC8377DA3085 /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = B0A32BF5A3FF4F0714FBD5AE9BC9D7C5 /* SDWebImageDownloaderResponseModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1B6CE67196EE181E6B56788EFC7E00D3 /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 3D25C0FEE4912F434715D7FADACA628C /* SDImageGIFCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1BC44E2FDD197D5210A23C9CCF1A906B /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A60F98CD3916F97DC932C53793A5F4F /* SDWebImageCompat.m */; };
		1C8B70C74291A3076746C3B18781568E /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B78C07859543713843BCE34D0DA5191 /* SDImageCachesManagerOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		20D618EF3EA5E3BE96DA24D36E3CA9EF /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 59697165E10C4C6366B266B95C5214E3 /* SDAsyncBlockOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		24E8E4ED0B5D988E3346E6638619F4E4 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = CD14D06DE9E5B4D29D59348B4FA4F5E8 /* SDImageFrame.m */; };
		288D796F3F7B9F42690E24A3B1018B2C /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 4ACB3D944EDF38E25E2FE2B8A262A9B9 /* SDImageIOAnimatedCoder.m */; };
		29681807EF0A9490F8E7D4CD82D32DF9 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */; };
		29F7F0E98FD26A96364DBACD7D5F237A /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 9205E8EDC209367A98C743C2D4ECAF19 /* SDWebImageDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2DDD48230ED9E8068C7E439D79B99A8E /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = C23EDBB1ACE39BB498E6A1518B9C74D3 /* SDInternalMacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		2F6D9BEA582A2DBB70A6C3B2FC2DB91E /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 267F27BA97CF4C4E7C8910653DBDBEA9 /* SDWebImageDownloaderResponseModifier.m */; };
		3187FF0C251D1B78BE87F64F6F6E944A /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 9540918B87A8F3C2462A2F3B70CF782A /* SDWebImageTransition.m */; };
		31DC2EC78AD1F8241AE6051EF9E73B0A /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = EA44DB38876627CD65ED6D637E751240 /* SDWebImageDefine.m */; };
		320DE42AF3CFE11FF785FEB1A7E6547B /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = A622D60101B5D96E47EF2E09B0B188D7 /* SDImageFramePool.m */; };
		32ACEDCEBE0507A82D6323114A1C74F1 /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = D67372342CAB70F81AF15EA7731B141B /* UIImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		******************************** /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = AB1716DD44EE5FB1CC62BB03D5A667C6 /* SDWebImageDownloaderDecryptor.m */; };
		33D3587AF629B2FA21554DA002D6ACB8 /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A1097F9A84C146B5E3EFE2C1AD0C285 /* SDImageCachesManager.m */; };
		34B28D4F0168194B6EFAC0520EB7A7F4 /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 521156B72D596A47AE26E69970200316 /* NSImage+Compatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36F4B09E7C71DCC5CEC6057814033C37 /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = ******************************** /* UIView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		38938E604A7D708E6378A44063EF3512 /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 191C6433ABE61A01D19F15467EDF179B /* UIImageView+WebCache.m */; };
		3A1AD84C0DC3C256418CC46739024E96 /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 9027578F072E34B550325FA707B882AE /* SDmetamacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		******************************** /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 4419AF0C4317EC65EFA3C9E881597E43 /* SDWebImageOptionsProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C7EAECB8C573E714C818BA04EB33773 /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 75DAFD998D4207D627065B8CA8CC8F57 /* UIImage+MultiFormat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C8F2F868D0C361CAF43E53CDB8EB631 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = 122748138CFBA85C1D3BB0BF653E510A /* SDWebImageCacheSerializer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3D0BBFEC1921CE71BC240DC18D8BE540 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2175BE4D196BE8580534D3F5DAD11B39 /* SDImageTransformer.m */; };
		416DA8B2997381F954DBA6E6A53DA4A2 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = D12694DB1518B6569217E0239CCB936D /* NSData+ImageContentType.m */; };
		425C9EA28FBEB7F7FC09A3F4A88C5955 /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 8FCF4D578E581CEE147D53180E62DC5C /* SDWebImageError.m */; };
		44CD842019B1CEA681F820F37A30B7C4 /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = 3496DDBF543BEDF2D00B2F041A89F873 /* SDImageFramePool.h */; settings = {ATTRIBUTES = (Private, ); }; };
		4688743B7B845309486559EB7BD5D147 /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = F4F780428FC9338AF05625BCB2AC3834 /* SDWebImageCompat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		48916DE9521F627589300512ECC2D4A5 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E4BF801E63F0DA92B1678EFB9179D2D /* NSButton+WebCache.m */; };
		4B2C2AE16AE3DDA7417AFCF7952588F1 /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 386D1691699A4E9572B04815A4EF9740 /* SDImageAssetManager.h */; settings = {ATTRIBUTES = (Private, ); }; };
		4D2C79AB2D24CFEC864F08D913CE7692 /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AC53C7052155430DB28D91BFCD44B0E7 /* SDImageCodersManager.m */; };
		4ED05DB3E43FF6AE1FA22130B2B50F05 /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = A481568FA2BABBC0319A6A2A11E29C0D /* UIImage+MemoryCacheCost.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5111A0A0934551CD2B9DDB1A1CA79FA7 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = D56A4006630375CC005D4E02B03773FA /* SDAnimatedImageRep.m */; };
		51439008EEB898B6A8C82CE1CD0BAF56 /* Pods-PhotoCC-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A9BB56DDE642F146975EC0FC6EEA659C /* Pods-PhotoCC-dummy.m */; };
		526485EF6D2B62B24DB59122FB94BD42 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AADD7510466115613A19B4D7019E32F /* SDDeviceHelper.m */; };
		5308E660E723C11E7691D311FD59C459 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = BDF46210308B9DBB4B6DFADC6E4123BD /* SDDisplayLink.m */; };
		53433003112C4FE271EC985803862B61 /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 5250EA4D49A59FFB24E0F6459FDE328D /* SDWebImageCacheKeyFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		55F7C7F055A18044497F8C88CAE34118 /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = A3494ECB2DAA76A1254514C8E05812BB /* SDImageCachesManagerOperation.m */; };
		58F7CE37BB4CB3BE806B68A502E6E1A7 /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 867C8A8347D77F6EFE9A191E719198F7 /* SDWeakProxy.h */; settings = {ATTRIBUTES = (Private, ); }; };
		596180E0EC9F46D12BA840DC4AA62659 /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = C7A56503EB5119541F41307098CDDBF7 /* UIImage+MemoryCacheCost.m */; };
		597E390C0BBB75B8045B651C487C2034 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = EFDF074B95A2923158A4BBDCF871F8A3 /* SDImageAWebPCoder.m */; };
		5C8279C226EB028B044C5A0F4AC5A91A /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = DC9DB4CE052506887570F060F0B8766E /* SDAssociatedObject.h */; settings = {ATTRIBUTES = (Private, ); }; };
		5DCBA14510E091D6A1CE499B08B794B5 /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = FD4FB12963474D615806CA06F0670707 /* UIImage+Metadata.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5E10328A83E05D0015D7459FAAEF121D /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0071155FC381590ECB0F3BC99BF5F1AA /* SDGraphicsImageRenderer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		616A8338C42FB01748DF1BDDA944858D /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 94B15E6AEA0F285C9DDDCC3995B224BB /* UIView+WebCache.m */; };
		62FE895DF9D65A2955A275D909ECBE18 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 577663DC18D27CD8D8FF4A7D9665CF06 /* SDAnimatedImageView.m */; };
		67178A8153B1A2F1D0D544B8093E23C5 /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 69FA231600141378304BB8FC8FD9B8F9 /* SDAnimatedImageView+WebCache.m */; };
		676775CB29378BB6CA3CA5992E9C6A99 /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 79C76370608CFCD381A9E448D0FC0AC4 /* SDImageIOAnimatedCoderInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		694B8697854A776E32032999B2EF1FEA /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 19C6C0B3CF18DD5646D1F2DDB2FAD808 /* UIImage+Metadata.m */; };
		69A06A02F52EB26259FAD1DF6B121BE1 /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 4601EB88FE2E479D0EC43E3241A3FDC4 /* SDCallbackQueue.m */; };
		69AB6A513D5F36D7360FEF4FDA1D60D0 /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 306ED67E334A482D7A3443BEBFCF313C /* UIView+WebCacheState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6A19379E3B0370EDA447743C9B1A1379 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 30B20C912ED3FA214525CBE801ADE13B /* UIImageView+HighlightedWebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6B0978C9398336656EE309E62060AEAB /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 743FCA054303578FA955127E880FD2FC /* SDImageAssetManager.m */; };
		6B5C3592B5E911E833D067D0BC785B1A /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 6597E4DF56E5AB23F2836339CE55F53A /* SDImageFrame.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6E66305665DBCFBCF5B2480BF705D500 /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = F22A6FFDD16179CC0C1923F9F3DE24FA /* SDWebImageTransition.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6EFEEE3AE22E97DCEC4F5A3B88F56FC7 /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = CB7C8DDF45AE66E6094ED1C378A19DF4 /* SDImageLoader.m */; };
		6F3637EE643EABB1DE9212EA68649A64 /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = BF5B0347F1230337A948AC4455CDAB05 /* UIColor+SDHexString.m */; };
		6FF66666F86A0F6A537A905125C03777 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 715CF08ED91ADF2E34DA6E36BD7EE579 /* ImageIO.framework */; };
		7074EA7FCC90B4967A437F5C43496828 /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = ADBDF0B16C89614D6A7FD647508E9ECA /* SDDisplayLink.h */; settings = {ATTRIBUTES = (Private, ); }; };
		711D32EF4A9901567A488291603BF906 /* SDWebImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 48A7EF24D96B3D1EBC2FE51F87C3C56E /* SDWebImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		717F76926C7BCB5B10C3037AD9239084 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = DA67F24A944757F9ABADE6D1FE163A00 /* SDImageIOCoder.m */; };
		71BEB1D9532900291A5A24B1C038516F /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = 5B6B200F2F8A879C665EE51B672FDEB9 /* UIColor+SDHexString.h */; settings = {ATTRIBUTES = (Private, ); }; };
		71F2B8CBB99087F348C472230200586F /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 04DAA49E46C322BCAF72072079E94BC4 /* SDGraphicsImageRenderer.m */; };
		7372E1708F34464734462850F62DB4A1 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */; };
		74C474676C69A80BEC29B0F55FDF4D19 /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C5BC6F53C07D4EA0DE91F71DD2715E9 /* UIView+WebCacheState.m */; };
		74E069F8C9E22C0E37F261A5AB03A613 /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 73C347FF113DA3F8B04A8DF2724F0102 /* SDWebImageDownloaderConfig.m */; };
		752822FE3F5092322D18FEC4533B79A9 /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BD51459D1238BB1274A16A94960AE59 /* SDWebImageDownloader.m */; };
		75771A97B77FA30A0175A81B480F80EF /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = D3D278DB4E188A5477ECEFF771DCFA8E /* UIImage+ForceDecode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7A4EB9ED5D4E03170FFE61FCB299687B /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 0700F475405D7579E74D52D049A89F92 /* SDAnimatedImagePlayer.m */; };
		7C45DBA62EE045C4922404182F6393B8 /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = D725B5E0FC0A522B847231BEF3BA78D8 /* SDWebImageError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		83530BF68848CD2C4A79A1FD69B304A5 /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 6402D6104280BDFD64083AE5E9CA3010 /* SDImageGIFCoder.m */; };
		854807558DCB972EDDFC1D00032BA6E4 /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 564E8788118C0CE1DFF2AE181B485280 /* SDWebImageDownloaderConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		85C0B4EE334B9972299E62DE61A4BB56 /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 571802531CBAA631A2BD5E6E0366181A /* SDImageLoadersManager.m */; };
		864972FB0DF4B464B1B505AA5F788E91 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = E0023DB4F9BFE6A16BE2D0DB500D17A5 /* SDInternalMacros.m */; };
		88473AE7C22F952DACB39FA0758D1624 /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F0217717A20E3B88144CD7C7BA419D38 /* SDMemoryCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		88A23DF6F5638AC66C28C4102824E8B5 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = B23E700B4EA5F872F648A42003E80CDD /* NSImage+Compatibility.m */; };
		8AF38EDB1E9BF0D334AEB23C488870B8 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D2606F72782B52B1FBC54890B1E71F7 /* NSData+ImageContentType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8D8AD606ECD8E1F247965CD43956D412 /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 77D302E8294799802C8D1E8F199E4738 /* UIImage+Transform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8E2BE70620A43365FAFC0D7AA9B90DD1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 4BC73F20A69D9EB94BA491B17C736234 /* PrivacyInfo.xcprivacy */; };
		906DCE66CD5BD236081D468616199BB7 /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 2ED721913502D80AA71F492125134906 /* SDWebImageOptionsProcessor.m */; };
		91AAF555B286FBF53E4F98D092B406BD /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = A0AE36E5BB8675CDDF513995F06E98AE /* SDWebImageTransitionInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		91E8B94F8E02ABF5197DF5AE7D0B3934 /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = AD220FAC0C58E06520B206C4993CA3C5 /* SDWebImageDownloaderDecryptor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		928371B066E1211CE87089668D5BCB4C /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 00B669E7653939FF242F08609EC5A944 /* SDDiskCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9345137ED10358B60E37D05FB6165759 /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 9486308E1713FD39650452BADDD0AAE1 /* SDFileAttributeHelper.m */; };
		96C94385F8786136B56A5098E7C4C0AD /* Pods-PhotoCC-PhotoCCUITests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 57DAF3CB59A82CD9A73B3B9EFAF62062 /* Pods-PhotoCC-PhotoCCUITests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		96E97174F4614FFA0649085022CB4AFE /* SDWebImage-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 739DF1D52AC24F6B17638560995C0160 /* SDWebImage-dummy.m */; };
		97235408E59E16C18B6BDA1D29E1CB26 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F60CE8A5BA035C87A14743A52F008D5 /* SDWebImageManager.m */; };
		97385A64CA020489951EF769392C6DCF /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = D3AD30E2AEFE4FEFED93250510D9B3C7 /* UIView+WebCacheOperation.m */; };
		99B47E42059D3444873CD97CA4CA64F0 /* Pods-PhotoCC-PhotoCCUITests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F0C7C4FEDED418B577C5B0188554D10A /* Pods-PhotoCC-PhotoCCUITests-dummy.m */; };
		9B3420DEB8A0CCB9E1241A669AEFCA8E /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F730CB965A96E675D0A0B53CAABD965 /* SDAnimatedImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B9343E8599EE5196BA75E842DCB48B7 /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = F05AB937BE7C7238E3CAECFF3B67EC4A /* NSBezierPath+SDRoundedCorners.h */; settings = {ATTRIBUTES = (Private, ); }; };
		9CE425B89294BE2C13E70A86E75B15CF /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BF93C5045484F1591E413242A2017A1 /* SDDiskCache.m */; };
		9DF446F8CA5BC4D4098766EC9063012C /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 022711CA5B03F1626F0EECDAB0632B24 /* SDWebImageOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A1560247914C760D9EE5F7A2392CC06C /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = 472B15A11813B3B1324910488B4C40C6 /* UIImage+GIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A839428F403C52D8AA3466B65E20C27A /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = B5FDD0DB82B63E2655992209D9729D23 /* NSButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A92AB5E65CA85947368E46E6627F1BFB /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A68D2B7346869E4B484C1E0B918458 /* UIButton+WebCache.m */; };
		A9A49E4A3BE8882F60DF32BAF39DE191 /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = CEDC3796B6B78E048E2AD2B4C2BEAA62 /* SDWebImageManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA1EA8F0F0470F1596B1FFA58ABF3375 /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A2D37F6145284B0CC2CB79DA37B9072 /* SDWebImageDownloaderOperation.m */; };
		ABCB80C4813C849FC93D57676820C907 /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 58E0F1A48B0BFFAC086DDF0074651180 /* SDImageCacheDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AC14E56ECA7A4980A8E1CA68E800B12C /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = B0DF9433E2A41A6E59B221E4A958F8BD /* SDWebImagePrefetcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AEAE4105E57A6834042DD51B93397EE3 /* Pods-PhotoCC-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 16A56B0443E75D3B0D7FFE5B895D51D7 /* Pods-PhotoCC-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B2704AFFC5CC053154839DB44924D255 /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 743FDB4E449FE9754E2296C01F19F11E /* SDImageCoderHelper.m */; };
		B331CE2D3DEB461E738B886086A365F9 /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = F1C64CDB559030A7AB4FA305D865C37C /* SDImageGraphics.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B4F231C5CBAB3D4A184699D0066E0E83 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = FBD1808129ABFE1189A05CB41DFEE57C /* SDImageAWebPCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B5AF87C11A465F666473F6191D173905 /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = ED125922FB25702F3C26C4BD2DED8061 /* UIView+WebCacheOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B66356D4E7E43B3D15324569AA7EBB05 /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 36BFE228A507A2A400618C8A65B23B1F /* SDWebImageDownloaderOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B741DBE2A466E6211F879EF997D9322D /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 52D40A144410114E32433CF22338FF78 /* SDImageCodersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B95C63A039D9D08896421291DEBD3AEB /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6E15E8212E79DB8310765810214DB5DF /* SDWebImageCacheKeyFilter.m */; };
		BADA31750A2136D073EDA4461DBE1EEA /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 9504B2AF8D26DB732BD02C2B79B9A407 /* UIButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BCDC1E1D46DD124B5726A064D2EE66A3 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = E5C91ABEBEF72E25932505AFEC393C59 /* UIImage+MultiFormat.m */; };
		BCEFDE57BB0E0B36731C8D39FFA1BE2C /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = ED7FAE053DEAAE4E60415D6230A1CA11 /* SDWebImageDownloaderRequestModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BDBE494BAC544843982C3CA96A6C41DD /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = D9533FE1BE067789E059D196DD138051 /* SDAnimatedImagePlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C1DD8C6A64F948E4C53560C76B995DA4 /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = 1FB006EEA81EB8CD9158583BCC884A07 /* SDAnimatedImageView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C2840BF1950FF7EE2DCD6D55F768A49C /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = C2C3E9EB7E42E0862F1F78C08BE02FAB /* UIImage+GIF.m */; };
		C6A100159974349FEAAC99B82BE0F872 /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 81E0A688A74C081D3148DA05F2C4CFCC /* SDImageLoader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C93E972E75F84674690300123984EC43 /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = C5F3EAEF13E02BF72CD113437A3C2155 /* SDAssociatedObject.m */; };
		CA1E0DCDF679EA2DE2ED0915426E1D04 /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = DDC89D3E31EECDFE45EDF12E0EB1BDF2 /* SDWeakProxy.m */; };
		CFF8D1A5E4C2097EF05E1021FE112886 /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = 09DDE2B18CE54FAA3A2A3CA63F90F524 /* SDWebImageIndicator.m */; };
		D06BB547D59D183FD1DDD84DEBAC9EE8 /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = CAAAF4D46AD5B8E6A23AE84A0D66ED02 /* SDWebImageCacheSerializer.m */; };
		D191A0CD1F2DB3EBA64A131CC239EFA8 /* Pods-PhotoCCTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D079B9E5F132F5277CC412D4E5B7F3E7 /* Pods-PhotoCCTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D2CD8848F856EC9942A76610AAE66F0A /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 080EF8B7475FDC22D26849A0239285D6 /* SDImageIOCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D62A672EEB252581BD972DDA862BE1DD /* SDWebImage-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = ACFC8C9017E4313795778CDADFA84D76 /* SDWebImage-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D662C83ECE8BEDA5FFB52F3575CA3E1A /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A1DA5A71080066F04581D99FB564890 /* SDImageCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D7B3E8948DB04BD8FB6748419DA03EA9 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = D519D3FE5B5AE20E1475F2896A1D8796 /* SDAnimatedImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DEA09692CF813A23899CD4949A9B6801 /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = 23AA9714783078CD4E0BCE2389816162 /* SDAnimatedImageRep.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E0BCF21E9FA59F638C13ECCECC4D9690 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 3B16223AF3ACC30852BB485A9563CA61 /* SDMemoryCache.m */; };
		E4F1B478580D6D7328BC29607BDE46F6 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 1F08F565517D5288B0DA9F6A12833BB5 /* UIImage+ExtendedCacheData.m */; };
		E50613C67DD02AF6EA825DA0B31EFFAD /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E41DBD1F1B0240F8D11B54364CD7F9F /* SDImageGraphics.m */; };
		E515931C2ABD3074169C8C7D41FF0EBE /* Pods-PhotoCCTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7F706A0C7C191ADCA6915C442210CD0E /* Pods-PhotoCCTests-dummy.m */; };
		E76969F9B01139118427505B18F9CD21 /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 594478697D79507754278692418D4FAB /* SDImageHEICCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E8AB529B9E0B4C23921344F6C4ABFEA4 /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = D58AB7740D866D9DE46D2E1D153BDB32 /* SDImageCoder.m */; };
		EA82B6D97C9C5D0558047AF552D63203 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 3D3421CC5E7D37A6B080551963E8CB75 /* SDWebImageDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EB4EBDDD9542C2ED5ACDE677442E056F /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */; };
		ECE64B732F9FA7C402DDEEC58DCB9D98 /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 92733644440AF5671140EF6182FC6899 /* SDImageAPNGCoder.m */; };
		ED8F64FF98CFAE0B12CF60A1B0E6BAF8 /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 0DF69FF05BE79483EF131B55A7D44B75 /* SDCallbackQueue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EF6A6C725598F572A70C5FCEE328C184 /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 4E9A608DCF04FD972479EBF6958081EB /* SDImageTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F49CB22863CCFEC7817D259F27F91C57 /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 20455AF618361234A6AE4AC7A588B868 /* SDWebImageIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F53BE4449AE5896F76325E4DCB6D0B13 /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 30602AA31F6831C4BF476D6F1135AE13 /* SDImageCachesManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F68889CD481716EE5D6B75EBD8FD53A6 /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0BBB30408413D4FED499FFF435F9D4DF /* SDImageCoderHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F969E61ABB5942AB8D94AAE3AB4147E9 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */; };
		FA3021DED76B9B182CC9195A60EB1209 /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = BB91C0A5DEEC606B430A399677D72E9F /* NSBezierPath+SDRoundedCorners.m */; };
		FCDEC6A53CF5517E1AF5B331FD65F6D9 /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE66DC0A1D6507B66D1A349C1165237 /* SDImageCacheConfig.m */; };
		FCEE5BD645E95FF55468C4AB6D17CFDA /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9569AC3F3CFFD7C8EF0385FA20D1B359 /* UIImageView+HighlightedWebCache.m */; };
		FEA8BA4F82CCBD1D28DCC7EF39FB4096 /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 73785F40EB08482D8DF2B9E8A8C8CFC7 /* SDImageCacheDefine.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7E80BC1C5D2C8FB368C470B193F71DFB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 94CFBA7D633ECA58DF85C327B035E6A3;
			remoteInfo = "SDWebImage-SDWebImage";
		};
		D3B8730050C0D25D601AB2E669DC7A49 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3847153A6E5EEFB86565BA840768F429;
			remoteInfo = SDWebImage;
		};
		EFB80E67904031A50B7E33FF507C33F1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BCB90ADD30A24F0E647E1B034A6C988C;
			remoteInfo = "Pods-PhotoCC";
		};
		FB538B0CC2E092CFD16909249C0483BA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3847153A6E5EEFB86565BA840768F429;
			remoteInfo = SDWebImage;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0071155FC381590ECB0F3BC99BF5F1AA /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDGraphicsImageRenderer.h; path = SDWebImage/Core/SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		00A68D2B7346869E4B484C1E0B918458 /* UIButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIButton+WebCache.m"; path = "SDWebImage/Core/UIButton+WebCache.m"; sourceTree = "<group>"; };
		00B669E7653939FF242F08609EC5A944 /* SDDiskCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDiskCache.h; path = SDWebImage/Core/SDDiskCache.h; sourceTree = "<group>"; };
		022711CA5B03F1626F0EECDAB0632B24 /* SDWebImageOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOperation.h; path = SDWebImage/Core/SDWebImageOperation.h; sourceTree = "<group>"; };
		02A4E1BF99CF3AC1C42CD5CBD83BFCCE /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoder.h; path = SDWebImage/Core/SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		030F4C8FEEB02A7EF488C74DEFE32EC3 /* SDDeviceHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDeviceHelper.h; path = SDWebImage/Private/SDDeviceHelper.h; sourceTree = "<group>"; };
		04DAA49E46C322BCAF72072079E94BC4 /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDGraphicsImageRenderer.m; path = SDWebImage/Core/SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		064E6871DF90C494746CC59AD26B7626 /* Pods_PhotoCC_PhotoCCUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCC_PhotoCCUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0700F475405D7579E74D52D049A89F92 /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImagePlayer.m; path = SDWebImage/Core/SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		080EF8B7475FDC22D26849A0239285D6 /* SDImageIOCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOCoder.h; path = SDWebImage/Core/SDImageIOCoder.h; sourceTree = "<group>"; };
		09DDE2B18CE54FAA3A2A3CA63F90F524 /* SDWebImageIndicator.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageIndicator.m; path = SDWebImage/Core/SDWebImageIndicator.m; sourceTree = "<group>"; };
		0BBB30408413D4FED499FFF435F9D4DF /* SDImageCoderHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoderHelper.h; path = SDWebImage/Core/SDImageCoderHelper.h; sourceTree = "<group>"; };
		0BF93C5045484F1591E413242A2017A1 /* SDDiskCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDiskCache.m; path = SDWebImage/Core/SDDiskCache.m; sourceTree = "<group>"; };
		0D9C95C7A6F7A628DD1E2C0C59CC20E0 /* SDImageCacheConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheConfig.h; path = SDWebImage/Core/SDImageCacheConfig.h; sourceTree = "<group>"; };
		0DF69FF05BE79483EF131B55A7D44B75 /* SDCallbackQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDCallbackQueue.h; path = SDWebImage/Core/SDCallbackQueue.h; sourceTree = "<group>"; };
		0E4BF801E63F0DA92B1678EFB9179D2D /* NSButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSButton+WebCache.m"; path = "SDWebImage/Core/NSButton+WebCache.m"; sourceTree = "<group>"; };
		0F60CE8A5BA035C87A14743A52F008D5 /* SDWebImageManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageManager.m; path = SDWebImage/Core/SDWebImageManager.m; sourceTree = "<group>"; };
		122748138CFBA85C1D3BB0BF653E510A /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheSerializer.h; path = SDWebImage/Core/SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		13BFDD71869A9E73B181F64BF551ECE1 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCC-PhotoCCUITests.debug.xcconfig"; sourceTree = "<group>"; };
		146DEF665685AA9003E6332F68D0F7B8 /* SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SDWebImage-Info.plist"; sourceTree = "<group>"; };
		16A56B0443E75D3B0D7FFE5B895D51D7 /* Pods-PhotoCC-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-PhotoCC-umbrella.h"; sourceTree = "<group>"; };
		175D7CEB240389FB15AF779F5651B90D /* Pods-PhotoCC-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-PhotoCC-frameworks.sh"; sourceTree = "<group>"; };
		******************************** /* UIView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCache.h"; path = "SDWebImage/Core/UIView+WebCache.h"; sourceTree = "<group>"; };
		191C6433ABE61A01D19F15467EDF179B /* UIImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+WebCache.m"; path = "SDWebImage/Core/UIImageView+WebCache.m"; sourceTree = "<group>"; };
		19C6C0B3CF18DD5646D1F2DDB2FAD808 /* UIImage+Metadata.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Metadata.m"; path = "SDWebImage/Core/UIImage+Metadata.m"; sourceTree = "<group>"; };
		1A1DA5A71080066F04581D99FB564890 /* SDImageCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCache.h; path = SDWebImage/Core/SDImageCache.h; sourceTree = "<group>"; };
		1BC7D3BE0A3EFECCBF135287AC0EA343 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SDWebImage-SDWebImage-Info.plist"; sourceTree = "<group>"; };
		1C5BC6F53C07D4EA0DE91F71DD2715E9 /* UIView+WebCacheState.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheState.m"; path = "SDWebImage/Core/UIView+WebCacheState.m"; sourceTree = "<group>"; };
		1DD6CA4DA79B56BF9831CA953A9244BC /* Pods-PhotoCC-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCC-acknowledgements.plist"; sourceTree = "<group>"; };
		1E41DBD1F1B0240F8D11B54364CD7F9F /* SDImageGraphics.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGraphics.m; path = SDWebImage/Core/SDImageGraphics.m; sourceTree = "<group>"; };
		1F08F565517D5288B0DA9F6A12833BB5 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ExtendedCacheData.m"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		1FB006EEA81EB8CD9158583BCC884A07 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageView.h; path = SDWebImage/Core/SDAnimatedImageView.h; sourceTree = "<group>"; };
		20455AF618361234A6AE4AC7A588B868 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageIndicator.h; path = SDWebImage/Core/SDWebImageIndicator.h; sourceTree = "<group>"; };
		2175BE4D196BE8580534D3F5DAD11B39 /* SDImageTransformer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageTransformer.m; path = SDWebImage/Core/SDImageTransformer.m; sourceTree = "<group>"; };
		23AA9714783078CD4E0BCE2389816162 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageRep.h; path = SDWebImage/Core/SDAnimatedImageRep.h; sourceTree = "<group>"; };
		24D286305B0E83E47229172BF7376DEC /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ExtendedCacheData.h"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		267F27BA97CF4C4E7C8910653DBDBEA9 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderResponseModifier.m; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		2A60F98CD3916F97DC932C53793A5F4F /* SDWebImageCompat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCompat.m; path = SDWebImage/Core/SDWebImageCompat.m; sourceTree = "<group>"; };
		2AEBD03FB2AA1BE01665E9168FAE2803 /* Pods-PhotoCC.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCC.debug.xcconfig"; sourceTree = "<group>"; };
		2B78C07859543713843BCE34D0DA5191 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManagerOperation.h; path = SDWebImage/Private/SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		2ED721913502D80AA71F492125134906 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOptionsProcessor.m; path = SDWebImage/Core/SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		2F04D3F6754C97BD9AB72E5D11B251B0 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ForceDecode.m"; path = "SDWebImage/Core/UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		30602AA31F6831C4BF476D6F1135AE13 /* SDImageCachesManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManager.h; path = SDWebImage/Core/SDImageCachesManager.h; sourceTree = "<group>"; };
		306ED67E334A482D7A3443BEBFCF313C /* UIView+WebCacheState.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheState.h"; path = "SDWebImage/Core/UIView+WebCacheState.h"; sourceTree = "<group>"; };
		30B20C912ED3FA214525CBE801ADE13B /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+HighlightedWebCache.h"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		339CE8ABB5BCDA4234DF01FEDCE3A1E0 /* UIImage+Transform.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Transform.m"; path = "SDWebImage/Core/UIImage+Transform.m"; sourceTree = "<group>"; };
		3496DDBF543BEDF2D00B2F041A89F873 /* SDImageFramePool.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFramePool.h; path = SDWebImage/Private/SDImageFramePool.h; sourceTree = "<group>"; };
		368B5D43CA52582F2F96F2A98FF35CD6 /* Pods-PhotoCC.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCC.release.xcconfig"; sourceTree = "<group>"; };
		36BFE228A507A2A400618C8A65B23B1F /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderOperation.h; path = SDWebImage/Core/SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		386D1691699A4E9572B04815A4EF9740 /* SDImageAssetManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAssetManager.h; path = SDWebImage/Private/SDImageAssetManager.h; sourceTree = "<group>"; };
		3B16223AF3ACC30852BB485A9563CA61 /* SDMemoryCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDMemoryCache.m; path = SDWebImage/Core/SDMemoryCache.m; sourceTree = "<group>"; };
		3D25C0FEE4912F434715D7FADACA628C /* SDImageGIFCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGIFCoder.h; path = SDWebImage/Core/SDImageGIFCoder.h; sourceTree = "<group>"; };
		3D3421CC5E7D37A6B080551963E8CB75 /* SDWebImageDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDefine.h; path = SDWebImage/Core/SDWebImageDefine.h; sourceTree = "<group>"; };
		3F730CB965A96E675D0A0B53CAABD965 /* SDAnimatedImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImage.h; path = SDWebImage/Core/SDAnimatedImage.h; sourceTree = "<group>"; };
		4419AF0C4317EC65EFA3C9E881597E43 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOptionsProcessor.h; path = SDWebImage/Core/SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		4601EB88FE2E479D0EC43E3241A3FDC4 /* SDCallbackQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDCallbackQueue.m; path = SDWebImage/Core/SDCallbackQueue.m; sourceTree = "<group>"; };
		472B15A11813B3B1324910488B4C40C6 /* UIImage+GIF.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+GIF.h"; path = "SDWebImage/Core/UIImage+GIF.h"; sourceTree = "<group>"; };
		472F2EC9D8BF90437E29C1537BC6D151 /* Pods-PhotoCC-PhotoCCUITests-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-PhotoCC-PhotoCCUITests-frameworks.sh"; sourceTree = "<group>"; };
		47F9F962DA8803BBBBCEA1861D326882 /* Pods_PhotoCCTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCCTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		48241B879B5957C45AC6C7D15C9E873A /* Pods-PhotoCC-PhotoCCUITests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCC-PhotoCCUITests-acknowledgements.plist"; sourceTree = "<group>"; };
		48A5A3B930FDA04E0F263D357DC7AEA0 /* Pods-PhotoCCTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCCTests-Info.plist"; sourceTree = "<group>"; };
		48A7EF24D96B3D1EBC2FE51F87C3C56E /* SDWebImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImage.h; path = WebImage/SDWebImage.h; sourceTree = "<group>"; };
		48D7B693B7689703DFA2D0321D436F58 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCC-PhotoCCUITests.release.xcconfig"; sourceTree = "<group>"; };
		4A2D37F6145284B0CC2CB79DA37B9072 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderOperation.m; path = SDWebImage/Core/SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		4ACB3D944EDF38E25E2FE2B8A262A9B9 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOAnimatedCoder.m; path = SDWebImage/Core/SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		4BC73F20A69D9EB94BA491B17C736234 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = WebImage/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		4E9A608DCF04FD972479EBF6958081EB /* SDImageTransformer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageTransformer.h; path = SDWebImage/Core/SDImageTransformer.h; sourceTree = "<group>"; };
		521156B72D596A47AE26E69970200316 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSImage+Compatibility.h"; path = "SDWebImage/Core/NSImage+Compatibility.h"; sourceTree = "<group>"; };
		5250EA4D49A59FFB24E0F6459FDE328D /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheKeyFilter.h; path = SDWebImage/Core/SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		52D40A144410114E32433CF22338FF78 /* SDImageCodersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCodersManager.h; path = SDWebImage/Core/SDImageCodersManager.h; sourceTree = "<group>"; };
		5534AD2F0C56CB874249984CAEAD9219 /* Pods-PhotoCCTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCCTests-acknowledgements.plist"; sourceTree = "<group>"; };
		564E8788118C0CE1DFF2AE181B485280 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderConfig.h; path = SDWebImage/Core/SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		571802531CBAA631A2BD5E6E0366181A /* SDImageLoadersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoadersManager.m; path = SDWebImage/Core/SDImageLoadersManager.m; sourceTree = "<group>"; };
		577663DC18D27CD8D8FF4A7D9665CF06 /* SDAnimatedImageView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageView.m; path = SDWebImage/Core/SDAnimatedImageView.m; sourceTree = "<group>"; };
		57DAF3CB59A82CD9A73B3B9EFAF62062 /* Pods-PhotoCC-PhotoCCUITests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-PhotoCC-PhotoCCUITests-umbrella.h"; sourceTree = "<group>"; };
		586C2139449D3D7569E45F055C6F7779 /* SDWebImage.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.debug.xcconfig; sourceTree = "<group>"; };
		58E0F1A48B0BFFAC086DDF0074651180 /* SDImageCacheDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheDefine.h; path = SDWebImage/Core/SDImageCacheDefine.h; sourceTree = "<group>"; };
		594478697D79507754278692418D4FAB /* SDImageHEICCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageHEICCoder.h; path = SDWebImage/Core/SDImageHEICCoder.h; sourceTree = "<group>"; };
		59697165E10C4C6366B266B95C5214E3 /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAsyncBlockOperation.h; path = SDWebImage/Private/SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		5B6B200F2F8A879C665EE51B672FDEB9 /* UIColor+SDHexString.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIColor+SDHexString.h"; path = "SDWebImage/Private/UIColor+SDHexString.h"; sourceTree = "<group>"; };
		5BE66DC0A1D6507B66D1A349C1165237 /* SDImageCacheConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheConfig.m; path = SDWebImage/Core/SDImageCacheConfig.m; sourceTree = "<group>"; };
		5E9A30FE556CB75D6CE9901DC59C4183 /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDFileAttributeHelper.h; path = SDWebImage/Private/SDFileAttributeHelper.h; sourceTree = "<group>"; };
		6402D6104280BDFD64083AE5E9CA3010 /* SDImageGIFCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGIFCoder.m; path = SDWebImage/Core/SDImageGIFCoder.m; sourceTree = "<group>"; };
		6597E4DF56E5AB23F2836339CE55F53A /* SDImageFrame.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFrame.h; path = SDWebImage/Core/SDImageFrame.h; sourceTree = "<group>"; };
		69FA231600141378304BB8FC8FD9B8F9 /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "SDAnimatedImageView+WebCache.m"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		6E15E8212E79DB8310765810214DB5DF /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheKeyFilter.m; path = SDWebImage/Core/SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		715CF08ED91ADF2E34DA6E36BD7EE579 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		726E4A93B81CA122D496C19B3C6A2B07 /* SDWebImageOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOperation.m; path = SDWebImage/Core/SDWebImageOperation.m; sourceTree = "<group>"; };
		73785F40EB08482D8DF2B9E8A8C8CFC7 /* SDImageCacheDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheDefine.m; path = SDWebImage/Core/SDImageCacheDefine.m; sourceTree = "<group>"; };
		739DF1D52AC24F6B17638560995C0160 /* SDWebImage-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SDWebImage-dummy.m"; sourceTree = "<group>"; };
		73C347FF113DA3F8B04A8DF2724F0102 /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderConfig.m; path = SDWebImage/Core/SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		743FCA054303578FA955127E880FD2FC /* SDImageAssetManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAssetManager.m; path = SDWebImage/Private/SDImageAssetManager.m; sourceTree = "<group>"; };
		743FDB4E449FE9754E2296C01F19F11E /* SDImageCoderHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoderHelper.m; path = SDWebImage/Core/SDImageCoderHelper.m; sourceTree = "<group>"; };
		75DAFD998D4207D627065B8CA8CC8F57 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MultiFormat.h"; path = "SDWebImage/Core/UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		77D302E8294799802C8D1E8F199E4738 /* UIImage+Transform.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Transform.h"; path = "SDWebImage/Core/UIImage+Transform.h"; sourceTree = "<group>"; };
		79C76370608CFCD381A9E448D0FC0AC4 /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoderInternal.h; path = SDWebImage/Private/SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		7DE30C924835526F0197235AADBFC6C5 /* SDWebImage-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-prefix.pch"; sourceTree = "<group>"; };
		7EF6A1EA36EF9F455CDFEFA6D9D7D288 /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImagePrefetcher.m; path = SDWebImage/Core/SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		7F706A0C7C191ADCA6915C442210CD0E /* Pods-PhotoCCTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-PhotoCCTests-dummy.m"; sourceTree = "<group>"; };
		81E0A688A74C081D3148DA05F2C4CFCC /* SDImageLoader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoader.h; path = SDWebImage/Core/SDImageLoader.h; sourceTree = "<group>"; };
		867C8A8347D77F6EFE9A191E719198F7 /* SDWeakProxy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWeakProxy.h; path = SDWebImage/Private/SDWeakProxy.h; sourceTree = "<group>"; };
		8A1097F9A84C146B5E3EFE2C1AD0C285 /* SDImageCachesManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManager.m; path = SDWebImage/Core/SDImageCachesManager.m; sourceTree = "<group>"; };
		8B3FD183CB0198334DE0732139437E16 /* Pods-PhotoCC-PhotoCCUITests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-PhotoCC-PhotoCCUITests.modulemap"; sourceTree = "<group>"; };
		8D2606F72782B52B1FBC54890B1E71F7 /* NSData+ImageContentType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSData+ImageContentType.h"; path = "SDWebImage/Core/NSData+ImageContentType.h"; sourceTree = "<group>"; };
		8FCF4D578E581CEE147D53180E62DC5C /* SDWebImageError.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageError.m; path = SDWebImage/Core/SDWebImageError.m; sourceTree = "<group>"; };
		9027578F072E34B550325FA707B882AE /* SDmetamacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDmetamacros.h; path = SDWebImage/Private/SDmetamacros.h; sourceTree = "<group>"; };
		9205E8EDC209367A98C743C2D4ECAF19 /* SDWebImageDownloader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloader.h; path = SDWebImage/Core/SDWebImageDownloader.h; sourceTree = "<group>"; };
		92733644440AF5671140EF6182FC6899 /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAPNGCoder.m; path = SDWebImage/Core/SDImageAPNGCoder.m; sourceTree = "<group>"; };
		9486308E1713FD39650452BADDD0AAE1 /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDFileAttributeHelper.m; path = SDWebImage/Private/SDFileAttributeHelper.m; sourceTree = "<group>"; };
		94B15E6AEA0F285C9DDDCC3995B224BB /* UIView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCache.m"; path = "SDWebImage/Core/UIView+WebCache.m"; sourceTree = "<group>"; };
		9504B2AF8D26DB732BD02C2B79B9A407 /* UIButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIButton+WebCache.h"; path = "SDWebImage/Core/UIButton+WebCache.h"; sourceTree = "<group>"; };
		9540918B87A8F3C2462A2F3B70CF782A /* SDWebImageTransition.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageTransition.m; path = SDWebImage/Core/SDWebImageTransition.m; sourceTree = "<group>"; };
		9569AC3F3CFFD7C8EF0385FA20D1B359 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+HighlightedWebCache.m"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		9AADD7510466115613A19B4D7019E32F /* SDDeviceHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDeviceHelper.m; path = SDWebImage/Private/SDDeviceHelper.m; sourceTree = "<group>"; };
		9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/Cocoa.framework; sourceTree = DEVELOPER_DIR; };
		9BD51459D1238BB1274A16A94960AE59 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloader.m; path = SDWebImage/Core/SDWebImageDownloader.m; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A0AE36E5BB8675CDDF513995F06E98AE /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransitionInternal.h; path = SDWebImage/Private/SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		A3494ECB2DAA76A1254514C8E05812BB /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManagerOperation.m; path = SDWebImage/Private/SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		A481568FA2BABBC0319A6A2A11E29C0D /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MemoryCacheCost.h"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		A622D60101B5D96E47EF2E09B0B188D7 /* SDImageFramePool.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFramePool.m; path = SDWebImage/Private/SDImageFramePool.m; sourceTree = "<group>"; };
		A99FF2F19EADABB75FAE98E08A996353 /* Pods-PhotoCCTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCCTests.debug.xcconfig"; sourceTree = "<group>"; };
		A9BB56DDE642F146975EC0FC6EEA659C /* Pods-PhotoCC-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-PhotoCC-dummy.m"; sourceTree = "<group>"; };
		AB1716DD44EE5FB1CC62BB03D5A667C6 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderDecryptor.m; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		AC53C7052155430DB28D91BFCD44B0E7 /* SDImageCodersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCodersManager.m; path = SDWebImage/Core/SDImageCodersManager.m; sourceTree = "<group>"; };
		AC752F1DC9476A73159A9F931106D070 /* SDImageHEICCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageHEICCoder.m; path = SDWebImage/Core/SDImageHEICCoder.m; sourceTree = "<group>"; };
		ACFC8C9017E4313795778CDADFA84D76 /* SDWebImage-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-umbrella.h"; sourceTree = "<group>"; };
		AD220FAC0C58E06520B206C4993CA3C5 /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderDecryptor.h; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		ADBDF0B16C89614D6A7FD647508E9ECA /* SDDisplayLink.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDisplayLink.h; path = SDWebImage/Private/SDDisplayLink.h; sourceTree = "<group>"; };
		B0101691E35DF1C2AF664C7246554E29 /* SDImageCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCache.m; path = SDWebImage/Core/SDImageCache.m; sourceTree = "<group>"; };
		B0A32BF5A3FF4F0714FBD5AE9BC9D7C5 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderResponseModifier.h; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		B0B214D775196BA7CA8E17E53048A493 /* SDWebImage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SDWebImage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0DF9433E2A41A6E59B221E4A958F8BD /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImagePrefetcher.h; path = SDWebImage/Core/SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		B220B76B1B25D9519542C28A3E41A57A /* SDImageCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoder.h; path = SDWebImage/Core/SDImageCoder.h; sourceTree = "<group>"; };
		B23E700B4EA5F872F648A42003E80CDD /* NSImage+Compatibility.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSImage+Compatibility.m"; path = "SDWebImage/Core/NSImage+Compatibility.m"; sourceTree = "<group>"; };
		B543F69D8B5F2D75F219691FB206F194 /* Pods-PhotoCCTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-PhotoCCTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		B5FDD0DB82B63E2655992209D9729D23 /* NSButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSButton+WebCache.h"; path = "SDWebImage/Core/NSButton+WebCache.h"; sourceTree = "<group>"; };
		B888D2602C43CA2D5887217FE74A179F /* Pods-PhotoCCTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PhotoCCTests.release.xcconfig"; sourceTree = "<group>"; };
		BADDA45F7BCD8396E0C59C6023494634 /* Pods_PhotoCC.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhotoCC.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BB91C0A5DEEC606B430A399677D72E9F /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSBezierPath+SDRoundedCorners.m"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		BC9367564F7FAD50D983FA0CD987496A /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAPNGCoder.h; path = SDWebImage/Core/SDImageAPNGCoder.h; sourceTree = "<group>"; };
		BDF46210308B9DBB4B6DFADC6E4123BD /* SDDisplayLink.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDisplayLink.m; path = SDWebImage/Private/SDDisplayLink.m; sourceTree = "<group>"; };
		BF5B0347F1230337A948AC4455CDAB05 /* UIColor+SDHexString.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIColor+SDHexString.m"; path = "SDWebImage/Private/UIColor+SDHexString.m"; sourceTree = "<group>"; };
		C23EDBB1ACE39BB498E6A1518B9C74D3 /* SDInternalMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDInternalMacros.h; path = SDWebImage/Private/SDInternalMacros.h; sourceTree = "<group>"; };
		C2C3E9EB7E42E0862F1F78C08BE02FAB /* UIImage+GIF.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+GIF.m"; path = "SDWebImage/Core/UIImage+GIF.m"; sourceTree = "<group>"; };
		C5F3EAEF13E02BF72CD113437A3C2155 /* SDAssociatedObject.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAssociatedObject.m; path = SDWebImage/Private/SDAssociatedObject.m; sourceTree = "<group>"; };
		C7A56503EB5119541F41307098CDDBF7 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MemoryCacheCost.m"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		CAAAF4D46AD5B8E6A23AE84A0D66ED02 /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheSerializer.m; path = SDWebImage/Core/SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		CAB1CF486E96034F0D5ACAFBECCEED8B /* Pods-PhotoCC-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCC-Info.plist"; sourceTree = "<group>"; };
		CB7C8DDF45AE66E6094ED1C378A19DF4 /* SDImageLoader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoader.m; path = SDWebImage/Core/SDImageLoader.m; sourceTree = "<group>"; };
		CD14D06DE9E5B4D29D59348B4FA4F5E8 /* SDImageFrame.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFrame.m; path = SDWebImage/Core/SDImageFrame.m; sourceTree = "<group>"; };
		CEDC3796B6B78E048E2AD2B4C2BEAA62 /* SDWebImageManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageManager.h; path = SDWebImage/Core/SDWebImageManager.h; sourceTree = "<group>"; };
		CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SDWebImage.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		CF79145918A07ADC8DD646FA2ABD3CD6 /* SDWebImage.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.release.xcconfig; sourceTree = "<group>"; };
		D079B9E5F132F5277CC412D4E5B7F3E7 /* Pods-PhotoCCTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-PhotoCCTests-umbrella.h"; sourceTree = "<group>"; };
		D12694DB1518B6569217E0239CCB936D /* NSData+ImageContentType.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSData+ImageContentType.m"; path = "SDWebImage/Core/NSData+ImageContentType.m"; sourceTree = "<group>"; };
		D30F9FB4033D31A05001011AF5316F19 /* Pods-PhotoCC-PhotoCCUITests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PhotoCC-PhotoCCUITests-Info.plist"; sourceTree = "<group>"; };
		D3AD30E2AEFE4FEFED93250510D9B3C7 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheOperation.m"; path = "SDWebImage/Core/UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		D3D278DB4E188A5477ECEFF771DCFA8E /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ForceDecode.h"; path = "SDWebImage/Core/UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		D519D3FE5B5AE20E1475F2896A1D8796 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "SDAnimatedImageView+WebCache.h"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		D568CABC5909AC2C82F66A642425CB60 /* Pods-PhotoCC-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-PhotoCC-acknowledgements.markdown"; sourceTree = "<group>"; };
		D56A4006630375CC005D4E02B03773FA /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageRep.m; path = SDWebImage/Core/SDAnimatedImageRep.m; sourceTree = "<group>"; };
		D58AB7740D866D9DE46D2E1D153BDB32 /* SDImageCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoder.m; path = SDWebImage/Core/SDImageCoder.m; sourceTree = "<group>"; };
		D67372342CAB70F81AF15EA7731B141B /* UIImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+WebCache.h"; path = "SDWebImage/Core/UIImageView+WebCache.h"; sourceTree = "<group>"; };
		D725B5E0FC0A522B847231BEF3BA78D8 /* SDWebImageError.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageError.h; path = SDWebImage/Core/SDWebImageError.h; sourceTree = "<group>"; };
		D9533FE1BE067789E059D196DD138051 /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImagePlayer.h; path = SDWebImage/Core/SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		DA67F24A944757F9ABADE6D1FE163A00 /* SDImageIOCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOCoder.m; path = SDWebImage/Core/SDImageIOCoder.m; sourceTree = "<group>"; };
		DC9DB4CE052506887570F060F0B8766E /* SDAssociatedObject.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAssociatedObject.h; path = SDWebImage/Private/SDAssociatedObject.h; sourceTree = "<group>"; };
		DDC89D3E31EECDFE45EDF12E0EB1BDF2 /* SDWeakProxy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWeakProxy.m; path = SDWebImage/Private/SDWeakProxy.m; sourceTree = "<group>"; };
		E0023DB4F9BFE6A16BE2D0DB500D17A5 /* SDInternalMacros.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDInternalMacros.m; path = SDWebImage/Private/SDInternalMacros.m; sourceTree = "<group>"; };
		E309BE29D3661A537CFBF81A004A7644 /* Pods-PhotoCC.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-PhotoCC.modulemap"; sourceTree = "<group>"; };
		E40EE9EF95B7ABCE8A9F458E0287C208 /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAsyncBlockOperation.m; path = SDWebImage/Private/SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		E50F18FFE26A06A3613A9AD987BEDA14 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderRequestModifier.m; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		E5C91ABEBEF72E25932505AFEC393C59 /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MultiFormat.m"; path = "SDWebImage/Core/UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		EA44DB38876627CD65ED6D637E751240 /* SDWebImageDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDefine.m; path = SDWebImage/Core/SDWebImageDefine.m; sourceTree = "<group>"; };
		EB1ABFC2E4E3A15C496D6C322D08AD46 /* SDAnimatedImage.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImage.m; path = SDWebImage/Core/SDAnimatedImage.m; sourceTree = "<group>"; };
		ED125922FB25702F3C26C4BD2DED8061 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheOperation.h"; path = "SDWebImage/Core/UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		ED7FAE053DEAAE4E60415D6230A1CA11 /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderRequestModifier.h; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		EFDF074B95A2923158A4BBDCF871F8A3 /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAWebPCoder.m; path = SDWebImage/Core/SDImageAWebPCoder.m; sourceTree = "<group>"; };
		F0217717A20E3B88144CD7C7BA419D38 /* SDMemoryCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDMemoryCache.h; path = SDWebImage/Core/SDMemoryCache.h; sourceTree = "<group>"; };
		F05AB937BE7C7238E3CAECFF3B67EC4A /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSBezierPath+SDRoundedCorners.h"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		F0C7C4FEDED418B577C5B0188554D10A /* Pods-PhotoCC-PhotoCCUITests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-PhotoCC-PhotoCCUITests-dummy.m"; sourceTree = "<group>"; };
		F1C64CDB559030A7AB4FA305D865C37C /* SDImageGraphics.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGraphics.h; path = SDWebImage/Core/SDImageGraphics.h; sourceTree = "<group>"; };
		F22A6FFDD16179CC0C1923F9F3DE24FA /* SDWebImageTransition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransition.h; path = SDWebImage/Core/SDWebImageTransition.h; sourceTree = "<group>"; };
		F2405F6402EE2FDD8ACBF7D16D306D98 /* SDWebImage.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SDWebImage.modulemap; sourceTree = "<group>"; };
		F4F780428FC9338AF05625BCB2AC3834 /* SDWebImageCompat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCompat.h; path = SDWebImage/Core/SDWebImageCompat.h; sourceTree = "<group>"; };
		FBD1808129ABFE1189A05CB41DFEE57C /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAWebPCoder.h; path = SDWebImage/Core/SDImageAWebPCoder.h; sourceTree = "<group>"; };
		FBEB9D0C3364E4678636E28075C5BE0A /* Pods-PhotoCCTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-PhotoCCTests.modulemap"; sourceTree = "<group>"; };
		FD4FB12963474D615806CA06F0670707 /* UIImage+Metadata.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Metadata.h"; path = "SDWebImage/Core/UIImage+Metadata.h"; sourceTree = "<group>"; };
		FDFD32CCBD2EC92772F44AFD2765A2B0 /* SDImageLoadersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoadersManager.h; path = SDWebImage/Core/SDImageLoadersManager.h; sourceTree = "<group>"; };
		FE70A6A5A23D1739C705A9BB87397C33 /* Pods-PhotoCC-PhotoCCUITests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-PhotoCC-PhotoCCUITests-acknowledgements.markdown"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		5B2821D975627C377EBC3184ECF39ACF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29681807EF0A9490F8E7D4CD82D32DF9 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6D47D76EB22E023FBAE54D4BBC344987 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB4EBDDD9542C2ED5ACDE677442E056F /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B9831A4622FE3CB482D52999536CCF95 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F969E61ABB5942AB8D94AAE3AB4147E9 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BE4B02A2D96469A6EA01B3EE819C960D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7372E1708F34464734462850F62DB4A1 /* Cocoa.framework in Frameworks */,
				6FF66666F86A0F6A537A905125C03777 /* ImageIO.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE3E08086C8160F5E5AB5BDDA53EC3A0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0AAB3EB6F2F34130469AE7C9BBBF31A5 /* OS X */ = {
			isa = PBXGroup;
			children = (
				9B798F838F8BF12305D73C8D9CD12FC1 /* Cocoa.framework */,
				715CF08ED91ADF2E34DA6E36BD7EE579 /* ImageIO.framework */,
			);
			name = "OS X";
			sourceTree = "<group>";
		};
		62B72C34195D25016CCD778E716479FF /* Pods */ = {
			isa = PBXGroup;
			children = (
				DCE140918C4D38911241B7756A80B347 /* SDWebImage */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		6FF82B92CF7F69A3E84B137FD3DE2D51 /* Products */ = {
			isa = PBXGroup;
			children = (
				BADDA45F7BCD8396E0C59C6023494634 /* Pods_PhotoCC.framework */,
				064E6871DF90C494746CC59AD26B7626 /* Pods_PhotoCC_PhotoCCUITests.framework */,
				47F9F962DA8803BBBBCEA1861D326882 /* Pods_PhotoCCTests.framework */,
				B0B214D775196BA7CA8E17E53048A493 /* SDWebImage.framework */,
				CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7FACCC1F0785E2118CB68C8E8B2F055D /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				BF29766D7A62D1BB00853C75D124D766 /* Pods-PhotoCC */,
				D813D5DAAE7A6B5268D4D77D26020F4A /* Pods-PhotoCC-PhotoCCUITests */,
				B205EDC2B573DDAFAD48C1029C69F13B /* Pods-PhotoCCTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		B205EDC2B573DDAFAD48C1029C69F13B /* Pods-PhotoCCTests */ = {
			isa = PBXGroup;
			children = (
				FBEB9D0C3364E4678636E28075C5BE0A /* Pods-PhotoCCTests.modulemap */,
				B543F69D8B5F2D75F219691FB206F194 /* Pods-PhotoCCTests-acknowledgements.markdown */,
				5534AD2F0C56CB874249984CAEAD9219 /* Pods-PhotoCCTests-acknowledgements.plist */,
				7F706A0C7C191ADCA6915C442210CD0E /* Pods-PhotoCCTests-dummy.m */,
				48A5A3B930FDA04E0F263D357DC7AEA0 /* Pods-PhotoCCTests-Info.plist */,
				D079B9E5F132F5277CC412D4E5B7F3E7 /* Pods-PhotoCCTests-umbrella.h */,
				A99FF2F19EADABB75FAE98E08A996353 /* Pods-PhotoCCTests.debug.xcconfig */,
				B888D2602C43CA2D5887217FE74A179F /* Pods-PhotoCCTests.release.xcconfig */,
			);
			name = "Pods-PhotoCCTests";
			path = "Target Support Files/Pods-PhotoCCTests";
			sourceTree = "<group>";
		};
		BF29766D7A62D1BB00853C75D124D766 /* Pods-PhotoCC */ = {
			isa = PBXGroup;
			children = (
				E309BE29D3661A537CFBF81A004A7644 /* Pods-PhotoCC.modulemap */,
				D568CABC5909AC2C82F66A642425CB60 /* Pods-PhotoCC-acknowledgements.markdown */,
				1DD6CA4DA79B56BF9831CA953A9244BC /* Pods-PhotoCC-acknowledgements.plist */,
				A9BB56DDE642F146975EC0FC6EEA659C /* Pods-PhotoCC-dummy.m */,
				175D7CEB240389FB15AF779F5651B90D /* Pods-PhotoCC-frameworks.sh */,
				CAB1CF486E96034F0D5ACAFBECCEED8B /* Pods-PhotoCC-Info.plist */,
				16A56B0443E75D3B0D7FFE5B895D51D7 /* Pods-PhotoCC-umbrella.h */,
				2AEBD03FB2AA1BE01665E9168FAE2803 /* Pods-PhotoCC.debug.xcconfig */,
				368B5D43CA52582F2F96F2A98FF35CD6 /* Pods-PhotoCC.release.xcconfig */,
			);
			name = "Pods-PhotoCC";
			path = "Target Support Files/Pods-PhotoCC";
			sourceTree = "<group>";
		};
		C080C886C9E2F80D52E3E86A62E5B711 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0AAB3EB6F2F34130469AE7C9BBBF31A5 /* OS X */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				C080C886C9E2F80D52E3E86A62E5B711 /* Frameworks */,
				62B72C34195D25016CCD778E716479FF /* Pods */,
				6FF82B92CF7F69A3E84B137FD3DE2D51 /* Products */,
				7FACCC1F0785E2118CB68C8E8B2F055D /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D09D787E95A74384A494A7EEE593C30A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1BC7D3BE0A3EFECCBF135287AC0EA343 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */,
				F2405F6402EE2FDD8ACBF7D16D306D98 /* SDWebImage.modulemap */,
				739DF1D52AC24F6B17638560995C0160 /* SDWebImage-dummy.m */,
				146DEF665685AA9003E6332F68D0F7B8 /* SDWebImage-Info.plist */,
				7DE30C924835526F0197235AADBFC6C5 /* SDWebImage-prefix.pch */,
				ACFC8C9017E4313795778CDADFA84D76 /* SDWebImage-umbrella.h */,
				586C2139449D3D7569E45F055C6F7779 /* SDWebImage.debug.xcconfig */,
				CF79145918A07ADC8DD646FA2ABD3CD6 /* SDWebImage.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SDWebImage";
			sourceTree = "<group>";
		};
		D813D5DAAE7A6B5268D4D77D26020F4A /* Pods-PhotoCC-PhotoCCUITests */ = {
			isa = PBXGroup;
			children = (
				8B3FD183CB0198334DE0732139437E16 /* Pods-PhotoCC-PhotoCCUITests.modulemap */,
				FE70A6A5A23D1739C705A9BB87397C33 /* Pods-PhotoCC-PhotoCCUITests-acknowledgements.markdown */,
				48241B879B5957C45AC6C7D15C9E873A /* Pods-PhotoCC-PhotoCCUITests-acknowledgements.plist */,
				F0C7C4FEDED418B577C5B0188554D10A /* Pods-PhotoCC-PhotoCCUITests-dummy.m */,
				472F2EC9D8BF90437E29C1537BC6D151 /* Pods-PhotoCC-PhotoCCUITests-frameworks.sh */,
				D30F9FB4033D31A05001011AF5316F19 /* Pods-PhotoCC-PhotoCCUITests-Info.plist */,
				57DAF3CB59A82CD9A73B3B9EFAF62062 /* Pods-PhotoCC-PhotoCCUITests-umbrella.h */,
				13BFDD71869A9E73B181F64BF551ECE1 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */,
				48D7B693B7689703DFA2D0321D436F58 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */,
			);
			name = "Pods-PhotoCC-PhotoCCUITests";
			path = "Target Support Files/Pods-PhotoCC-PhotoCCUITests";
			sourceTree = "<group>";
		};
		DCE140918C4D38911241B7756A80B347 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				EB312AAD49A57024B7240685FE2B4BD6 /* Core */,
				D09D787E95A74384A494A7EEE593C30A /* Support Files */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		EB312AAD49A57024B7240685FE2B4BD6 /* Core */ = {
			isa = PBXGroup;
			children = (
				F05AB937BE7C7238E3CAECFF3B67EC4A /* NSBezierPath+SDRoundedCorners.h */,
				BB91C0A5DEEC606B430A399677D72E9F /* NSBezierPath+SDRoundedCorners.m */,
				B5FDD0DB82B63E2655992209D9729D23 /* NSButton+WebCache.h */,
				0E4BF801E63F0DA92B1678EFB9179D2D /* NSButton+WebCache.m */,
				8D2606F72782B52B1FBC54890B1E71F7 /* NSData+ImageContentType.h */,
				D12694DB1518B6569217E0239CCB936D /* NSData+ImageContentType.m */,
				521156B72D596A47AE26E69970200316 /* NSImage+Compatibility.h */,
				B23E700B4EA5F872F648A42003E80CDD /* NSImage+Compatibility.m */,
				3F730CB965A96E675D0A0B53CAABD965 /* SDAnimatedImage.h */,
				EB1ABFC2E4E3A15C496D6C322D08AD46 /* SDAnimatedImage.m */,
				D9533FE1BE067789E059D196DD138051 /* SDAnimatedImagePlayer.h */,
				0700F475405D7579E74D52D049A89F92 /* SDAnimatedImagePlayer.m */,
				23AA9714783078CD4E0BCE2389816162 /* SDAnimatedImageRep.h */,
				D56A4006630375CC005D4E02B03773FA /* SDAnimatedImageRep.m */,
				1FB006EEA81EB8CD9158583BCC884A07 /* SDAnimatedImageView.h */,
				577663DC18D27CD8D8FF4A7D9665CF06 /* SDAnimatedImageView.m */,
				D519D3FE5B5AE20E1475F2896A1D8796 /* SDAnimatedImageView+WebCache.h */,
				69FA231600141378304BB8FC8FD9B8F9 /* SDAnimatedImageView+WebCache.m */,
				DC9DB4CE052506887570F060F0B8766E /* SDAssociatedObject.h */,
				C5F3EAEF13E02BF72CD113437A3C2155 /* SDAssociatedObject.m */,
				59697165E10C4C6366B266B95C5214E3 /* SDAsyncBlockOperation.h */,
				E40EE9EF95B7ABCE8A9F458E0287C208 /* SDAsyncBlockOperation.m */,
				0DF69FF05BE79483EF131B55A7D44B75 /* SDCallbackQueue.h */,
				4601EB88FE2E479D0EC43E3241A3FDC4 /* SDCallbackQueue.m */,
				030F4C8FEEB02A7EF488C74DEFE32EC3 /* SDDeviceHelper.h */,
				9AADD7510466115613A19B4D7019E32F /* SDDeviceHelper.m */,
				00B669E7653939FF242F08609EC5A944 /* SDDiskCache.h */,
				0BF93C5045484F1591E413242A2017A1 /* SDDiskCache.m */,
				ADBDF0B16C89614D6A7FD647508E9ECA /* SDDisplayLink.h */,
				BDF46210308B9DBB4B6DFADC6E4123BD /* SDDisplayLink.m */,
				5E9A30FE556CB75D6CE9901DC59C4183 /* SDFileAttributeHelper.h */,
				9486308E1713FD39650452BADDD0AAE1 /* SDFileAttributeHelper.m */,
				0071155FC381590ECB0F3BC99BF5F1AA /* SDGraphicsImageRenderer.h */,
				04DAA49E46C322BCAF72072079E94BC4 /* SDGraphicsImageRenderer.m */,
				BC9367564F7FAD50D983FA0CD987496A /* SDImageAPNGCoder.h */,
				92733644440AF5671140EF6182FC6899 /* SDImageAPNGCoder.m */,
				386D1691699A4E9572B04815A4EF9740 /* SDImageAssetManager.h */,
				743FCA054303578FA955127E880FD2FC /* SDImageAssetManager.m */,
				FBD1808129ABFE1189A05CB41DFEE57C /* SDImageAWebPCoder.h */,
				EFDF074B95A2923158A4BBDCF871F8A3 /* SDImageAWebPCoder.m */,
				1A1DA5A71080066F04581D99FB564890 /* SDImageCache.h */,
				B0101691E35DF1C2AF664C7246554E29 /* SDImageCache.m */,
				0D9C95C7A6F7A628DD1E2C0C59CC20E0 /* SDImageCacheConfig.h */,
				5BE66DC0A1D6507B66D1A349C1165237 /* SDImageCacheConfig.m */,
				58E0F1A48B0BFFAC086DDF0074651180 /* SDImageCacheDefine.h */,
				73785F40EB08482D8DF2B9E8A8C8CFC7 /* SDImageCacheDefine.m */,
				30602AA31F6831C4BF476D6F1135AE13 /* SDImageCachesManager.h */,
				8A1097F9A84C146B5E3EFE2C1AD0C285 /* SDImageCachesManager.m */,
				2B78C07859543713843BCE34D0DA5191 /* SDImageCachesManagerOperation.h */,
				A3494ECB2DAA76A1254514C8E05812BB /* SDImageCachesManagerOperation.m */,
				B220B76B1B25D9519542C28A3E41A57A /* SDImageCoder.h */,
				D58AB7740D866D9DE46D2E1D153BDB32 /* SDImageCoder.m */,
				0BBB30408413D4FED499FFF435F9D4DF /* SDImageCoderHelper.h */,
				743FDB4E449FE9754E2296C01F19F11E /* SDImageCoderHelper.m */,
				52D40A144410114E32433CF22338FF78 /* SDImageCodersManager.h */,
				AC53C7052155430DB28D91BFCD44B0E7 /* SDImageCodersManager.m */,
				6597E4DF56E5AB23F2836339CE55F53A /* SDImageFrame.h */,
				CD14D06DE9E5B4D29D59348B4FA4F5E8 /* SDImageFrame.m */,
				3496DDBF543BEDF2D00B2F041A89F873 /* SDImageFramePool.h */,
				A622D60101B5D96E47EF2E09B0B188D7 /* SDImageFramePool.m */,
				3D25C0FEE4912F434715D7FADACA628C /* SDImageGIFCoder.h */,
				6402D6104280BDFD64083AE5E9CA3010 /* SDImageGIFCoder.m */,
				F1C64CDB559030A7AB4FA305D865C37C /* SDImageGraphics.h */,
				1E41DBD1F1B0240F8D11B54364CD7F9F /* SDImageGraphics.m */,
				594478697D79507754278692418D4FAB /* SDImageHEICCoder.h */,
				AC752F1DC9476A73159A9F931106D070 /* SDImageHEICCoder.m */,
				02A4E1BF99CF3AC1C42CD5CBD83BFCCE /* SDImageIOAnimatedCoder.h */,
				4ACB3D944EDF38E25E2FE2B8A262A9B9 /* SDImageIOAnimatedCoder.m */,
				79C76370608CFCD381A9E448D0FC0AC4 /* SDImageIOAnimatedCoderInternal.h */,
				080EF8B7475FDC22D26849A0239285D6 /* SDImageIOCoder.h */,
				DA67F24A944757F9ABADE6D1FE163A00 /* SDImageIOCoder.m */,
				81E0A688A74C081D3148DA05F2C4CFCC /* SDImageLoader.h */,
				CB7C8DDF45AE66E6094ED1C378A19DF4 /* SDImageLoader.m */,
				FDFD32CCBD2EC92772F44AFD2765A2B0 /* SDImageLoadersManager.h */,
				571802531CBAA631A2BD5E6E0366181A /* SDImageLoadersManager.m */,
				4E9A608DCF04FD972479EBF6958081EB /* SDImageTransformer.h */,
				2175BE4D196BE8580534D3F5DAD11B39 /* SDImageTransformer.m */,
				C23EDBB1ACE39BB498E6A1518B9C74D3 /* SDInternalMacros.h */,
				E0023DB4F9BFE6A16BE2D0DB500D17A5 /* SDInternalMacros.m */,
				F0217717A20E3B88144CD7C7BA419D38 /* SDMemoryCache.h */,
				3B16223AF3ACC30852BB485A9563CA61 /* SDMemoryCache.m */,
				9027578F072E34B550325FA707B882AE /* SDmetamacros.h */,
				867C8A8347D77F6EFE9A191E719198F7 /* SDWeakProxy.h */,
				DDC89D3E31EECDFE45EDF12E0EB1BDF2 /* SDWeakProxy.m */,
				48A7EF24D96B3D1EBC2FE51F87C3C56E /* SDWebImage.h */,
				5250EA4D49A59FFB24E0F6459FDE328D /* SDWebImageCacheKeyFilter.h */,
				6E15E8212E79DB8310765810214DB5DF /* SDWebImageCacheKeyFilter.m */,
				122748138CFBA85C1D3BB0BF653E510A /* SDWebImageCacheSerializer.h */,
				CAAAF4D46AD5B8E6A23AE84A0D66ED02 /* SDWebImageCacheSerializer.m */,
				F4F780428FC9338AF05625BCB2AC3834 /* SDWebImageCompat.h */,
				2A60F98CD3916F97DC932C53793A5F4F /* SDWebImageCompat.m */,
				3D3421CC5E7D37A6B080551963E8CB75 /* SDWebImageDefine.h */,
				EA44DB38876627CD65ED6D637E751240 /* SDWebImageDefine.m */,
				9205E8EDC209367A98C743C2D4ECAF19 /* SDWebImageDownloader.h */,
				9BD51459D1238BB1274A16A94960AE59 /* SDWebImageDownloader.m */,
				564E8788118C0CE1DFF2AE181B485280 /* SDWebImageDownloaderConfig.h */,
				73C347FF113DA3F8B04A8DF2724F0102 /* SDWebImageDownloaderConfig.m */,
				AD220FAC0C58E06520B206C4993CA3C5 /* SDWebImageDownloaderDecryptor.h */,
				AB1716DD44EE5FB1CC62BB03D5A667C6 /* SDWebImageDownloaderDecryptor.m */,
				36BFE228A507A2A400618C8A65B23B1F /* SDWebImageDownloaderOperation.h */,
				4A2D37F6145284B0CC2CB79DA37B9072 /* SDWebImageDownloaderOperation.m */,
				ED7FAE053DEAAE4E60415D6230A1CA11 /* SDWebImageDownloaderRequestModifier.h */,
				E50F18FFE26A06A3613A9AD987BEDA14 /* SDWebImageDownloaderRequestModifier.m */,
				B0A32BF5A3FF4F0714FBD5AE9BC9D7C5 /* SDWebImageDownloaderResponseModifier.h */,
				267F27BA97CF4C4E7C8910653DBDBEA9 /* SDWebImageDownloaderResponseModifier.m */,
				D725B5E0FC0A522B847231BEF3BA78D8 /* SDWebImageError.h */,
				8FCF4D578E581CEE147D53180E62DC5C /* SDWebImageError.m */,
				20455AF618361234A6AE4AC7A588B868 /* SDWebImageIndicator.h */,
				09DDE2B18CE54FAA3A2A3CA63F90F524 /* SDWebImageIndicator.m */,
				CEDC3796B6B78E048E2AD2B4C2BEAA62 /* SDWebImageManager.h */,
				0F60CE8A5BA035C87A14743A52F008D5 /* SDWebImageManager.m */,
				022711CA5B03F1626F0EECDAB0632B24 /* SDWebImageOperation.h */,
				726E4A93B81CA122D496C19B3C6A2B07 /* SDWebImageOperation.m */,
				4419AF0C4317EC65EFA3C9E881597E43 /* SDWebImageOptionsProcessor.h */,
				2ED721913502D80AA71F492125134906 /* SDWebImageOptionsProcessor.m */,
				B0DF9433E2A41A6E59B221E4A958F8BD /* SDWebImagePrefetcher.h */,
				7EF6A1EA36EF9F455CDFEFA6D9D7D288 /* SDWebImagePrefetcher.m */,
				F22A6FFDD16179CC0C1923F9F3DE24FA /* SDWebImageTransition.h */,
				9540918B87A8F3C2462A2F3B70CF782A /* SDWebImageTransition.m */,
				A0AE36E5BB8675CDDF513995F06E98AE /* SDWebImageTransitionInternal.h */,
				9504B2AF8D26DB732BD02C2B79B9A407 /* UIButton+WebCache.h */,
				00A68D2B7346869E4B484C1E0B918458 /* UIButton+WebCache.m */,
				5B6B200F2F8A879C665EE51B672FDEB9 /* UIColor+SDHexString.h */,
				BF5B0347F1230337A948AC4455CDAB05 /* UIColor+SDHexString.m */,
				24D286305B0E83E47229172BF7376DEC /* UIImage+ExtendedCacheData.h */,
				1F08F565517D5288B0DA9F6A12833BB5 /* UIImage+ExtendedCacheData.m */,
				D3D278DB4E188A5477ECEFF771DCFA8E /* UIImage+ForceDecode.h */,
				2F04D3F6754C97BD9AB72E5D11B251B0 /* UIImage+ForceDecode.m */,
				472B15A11813B3B1324910488B4C40C6 /* UIImage+GIF.h */,
				C2C3E9EB7E42E0862F1F78C08BE02FAB /* UIImage+GIF.m */,
				A481568FA2BABBC0319A6A2A11E29C0D /* UIImage+MemoryCacheCost.h */,
				C7A56503EB5119541F41307098CDDBF7 /* UIImage+MemoryCacheCost.m */,
				FD4FB12963474D615806CA06F0670707 /* UIImage+Metadata.h */,
				19C6C0B3CF18DD5646D1F2DDB2FAD808 /* UIImage+Metadata.m */,
				75DAFD998D4207D627065B8CA8CC8F57 /* UIImage+MultiFormat.h */,
				E5C91ABEBEF72E25932505AFEC393C59 /* UIImage+MultiFormat.m */,
				77D302E8294799802C8D1E8F199E4738 /* UIImage+Transform.h */,
				339CE8ABB5BCDA4234DF01FEDCE3A1E0 /* UIImage+Transform.m */,
				30B20C912ED3FA214525CBE801ADE13B /* UIImageView+HighlightedWebCache.h */,
				9569AC3F3CFFD7C8EF0385FA20D1B359 /* UIImageView+HighlightedWebCache.m */,
				D67372342CAB70F81AF15EA7731B141B /* UIImageView+WebCache.h */,
				191C6433ABE61A01D19F15467EDF179B /* UIImageView+WebCache.m */,
				******************************** /* UIView+WebCache.h */,
				94B15E6AEA0F285C9DDDCC3995B224BB /* UIView+WebCache.m */,
				ED125922FB25702F3C26C4BD2DED8061 /* UIView+WebCacheOperation.h */,
				D3AD30E2AEFE4FEFED93250510D9B3C7 /* UIView+WebCacheOperation.m */,
				306ED67E334A482D7A3443BEBFCF313C /* UIView+WebCacheState.h */,
				1C5BC6F53C07D4EA0DE91F71DD2715E9 /* UIView+WebCacheState.m */,
				EF6BB58B580ED2ABE293870DB6FF5F9E /* Resources */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		EF6BB58B580ED2ABE293870DB6FF5F9E /* Resources */ = {
			isa = PBXGroup;
			children = (
				4BC73F20A69D9EB94BA491B17C736234 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		24BC5362AFDB902E84C891319577C021 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96C94385F8786136B56A5098E7C4C0AD /* Pods-PhotoCC-PhotoCCUITests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33783D69751B087D045FCF1FCA02E724 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B9343E8599EE5196BA75E842DCB48B7 /* NSBezierPath+SDRoundedCorners.h in Headers */,
				A839428F403C52D8AA3466B65E20C27A /* NSButton+WebCache.h in Headers */,
				8AF38EDB1E9BF0D334AEB23C488870B8 /* NSData+ImageContentType.h in Headers */,
				34B28D4F0168194B6EFAC0520EB7A7F4 /* NSImage+Compatibility.h in Headers */,
				9B3420DEB8A0CCB9E1241A669AEFCA8E /* SDAnimatedImage.h in Headers */,
				BDBE494BAC544843982C3CA96A6C41DD /* SDAnimatedImagePlayer.h in Headers */,
				DEA09692CF813A23899CD4949A9B6801 /* SDAnimatedImageRep.h in Headers */,
				C1DD8C6A64F948E4C53560C76B995DA4 /* SDAnimatedImageView.h in Headers */,
				D7B3E8948DB04BD8FB6748419DA03EA9 /* SDAnimatedImageView+WebCache.h in Headers */,
				5C8279C226EB028B044C5A0F4AC5A91A /* SDAssociatedObject.h in Headers */,
				20D618EF3EA5E3BE96DA24D36E3CA9EF /* SDAsyncBlockOperation.h in Headers */,
				ED8F64FF98CFAE0B12CF60A1B0E6BAF8 /* SDCallbackQueue.h in Headers */,
				042D40751BD2F51FBE9FECD4707CBBE9 /* SDDeviceHelper.h in Headers */,
				928371B066E1211CE87089668D5BCB4C /* SDDiskCache.h in Headers */,
				7074EA7FCC90B4967A437F5C43496828 /* SDDisplayLink.h in Headers */,
				0453019EC6578A67B82CF569EC765546 /* SDFileAttributeHelper.h in Headers */,
				5E10328A83E05D0015D7459FAAEF121D /* SDGraphicsImageRenderer.h in Headers */,
				165F1C9CBD621828C788A3018D0426C5 /* SDImageAPNGCoder.h in Headers */,
				4B2C2AE16AE3DDA7417AFCF7952588F1 /* SDImageAssetManager.h in Headers */,
				B4F231C5CBAB3D4A184699D0066E0E83 /* SDImageAWebPCoder.h in Headers */,
				D662C83ECE8BEDA5FFB52F3575CA3E1A /* SDImageCache.h in Headers */,
				14CA284AC4FF1EED75E785641EE98034 /* SDImageCacheConfig.h in Headers */,
				ABCB80C4813C849FC93D57676820C907 /* SDImageCacheDefine.h in Headers */,
				F53BE4449AE5896F76325E4DCB6D0B13 /* SDImageCachesManager.h in Headers */,
				1C8B70C74291A3076746C3B18781568E /* SDImageCachesManagerOperation.h in Headers */,
				09A2ACBC8CE1761652EAA20886AEFE10 /* SDImageCoder.h in Headers */,
				F68889CD481716EE5D6B75EBD8FD53A6 /* SDImageCoderHelper.h in Headers */,
				B741DBE2A466E6211F879EF997D9322D /* SDImageCodersManager.h in Headers */,
				6B5C3592B5E911E833D067D0BC785B1A /* SDImageFrame.h in Headers */,
				44CD842019B1CEA681F820F37A30B7C4 /* SDImageFramePool.h in Headers */,
				1B6CE67196EE181E6B56788EFC7E00D3 /* SDImageGIFCoder.h in Headers */,
				B331CE2D3DEB461E738B886086A365F9 /* SDImageGraphics.h in Headers */,
				E76969F9B01139118427505B18F9CD21 /* SDImageHEICCoder.h in Headers */,
				089F3C4BAA46A37EC5763DD312771021 /* SDImageIOAnimatedCoder.h in Headers */,
				676775CB29378BB6CA3CA5992E9C6A99 /* SDImageIOAnimatedCoderInternal.h in Headers */,
				D2CD8848F856EC9942A76610AAE66F0A /* SDImageIOCoder.h in Headers */,
				C6A100159974349FEAAC99B82BE0F872 /* SDImageLoader.h in Headers */,
				10017B43AC38C3A89D7AC1376C6E7066 /* SDImageLoadersManager.h in Headers */,
				EF6A6C725598F572A70C5FCEE328C184 /* SDImageTransformer.h in Headers */,
				2DDD48230ED9E8068C7E439D79B99A8E /* SDInternalMacros.h in Headers */,
				88473AE7C22F952DACB39FA0758D1624 /* SDMemoryCache.h in Headers */,
				3A1AD84C0DC3C256418CC46739024E96 /* SDmetamacros.h in Headers */,
				58F7CE37BB4CB3BE806B68A502E6E1A7 /* SDWeakProxy.h in Headers */,
				711D32EF4A9901567A488291603BF906 /* SDWebImage.h in Headers */,
				D62A672EEB252581BD972DDA862BE1DD /* SDWebImage-umbrella.h in Headers */,
				53433003112C4FE271EC985803862B61 /* SDWebImageCacheKeyFilter.h in Headers */,
				3C8F2F868D0C361CAF43E53CDB8EB631 /* SDWebImageCacheSerializer.h in Headers */,
				4688743B7B845309486559EB7BD5D147 /* SDWebImageCompat.h in Headers */,
				EA82B6D97C9C5D0558047AF552D63203 /* SDWebImageDefine.h in Headers */,
				29F7F0E98FD26A96364DBACD7D5F237A /* SDWebImageDownloader.h in Headers */,
				854807558DCB972EDDFC1D00032BA6E4 /* SDWebImageDownloaderConfig.h in Headers */,
				91E8B94F8E02ABF5197DF5AE7D0B3934 /* SDWebImageDownloaderDecryptor.h in Headers */,
				B66356D4E7E43B3D15324569AA7EBB05 /* SDWebImageDownloaderOperation.h in Headers */,
				BCEFDE57BB0E0B36731C8D39FFA1BE2C /* SDWebImageDownloaderRequestModifier.h in Headers */,
				18AD90784D549657DF51BC8377DA3085 /* SDWebImageDownloaderResponseModifier.h in Headers */,
				7C45DBA62EE045C4922404182F6393B8 /* SDWebImageError.h in Headers */,
				F49CB22863CCFEC7817D259F27F91C57 /* SDWebImageIndicator.h in Headers */,
				A9A49E4A3BE8882F60DF32BAF39DE191 /* SDWebImageManager.h in Headers */,
				9DF446F8CA5BC4D4098766EC9063012C /* SDWebImageOperation.h in Headers */,
				******************************** /* SDWebImageOptionsProcessor.h in Headers */,
				AC14E56ECA7A4980A8E1CA68E800B12C /* SDWebImagePrefetcher.h in Headers */,
				6E66305665DBCFBCF5B2480BF705D500 /* SDWebImageTransition.h in Headers */,
				91AAF555B286FBF53E4F98D092B406BD /* SDWebImageTransitionInternal.h in Headers */,
				BADA31750A2136D073EDA4461DBE1EEA /* UIButton+WebCache.h in Headers */,
				71BEB1D9532900291A5A24B1C038516F /* UIColor+SDHexString.h in Headers */,
				1830558A4D2D63C8E76BC3136D8213F9 /* UIImage+ExtendedCacheData.h in Headers */,
				75771A97B77FA30A0175A81B480F80EF /* UIImage+ForceDecode.h in Headers */,
				A1560247914C760D9EE5F7A2392CC06C /* UIImage+GIF.h in Headers */,
				4ED05DB3E43FF6AE1FA22130B2B50F05 /* UIImage+MemoryCacheCost.h in Headers */,
				5DCBA14510E091D6A1CE499B08B794B5 /* UIImage+Metadata.h in Headers */,
				3C7EAECB8C573E714C818BA04EB33773 /* UIImage+MultiFormat.h in Headers */,
				8D8AD606ECD8E1F247965CD43956D412 /* UIImage+Transform.h in Headers */,
				6A19379E3B0370EDA447743C9B1A1379 /* UIImageView+HighlightedWebCache.h in Headers */,
				32ACEDCEBE0507A82D6323114A1C74F1 /* UIImageView+WebCache.h in Headers */,
				36F4B09E7C71DCC5CEC6057814033C37 /* UIView+WebCache.h in Headers */,
				B5AF87C11A465F666473F6191D173905 /* UIView+WebCacheOperation.h in Headers */,
				69AB6A513D5F36D7360FEF4FDA1D60D0 /* UIView+WebCacheState.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3838A062A6C2B3B674040149F8276915 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D191A0CD1F2DB3EBA64A131CC239EFA8 /* Pods-PhotoCCTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		918F044567ADE40D91A64D5F01125A6D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AEAE4105E57A6834042DD51B93397EE3 /* Pods-PhotoCC-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		3847153A6E5EEFB86565BA840768F429 /* SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2F9E94B5F79E365095CB33D3D3FCA6A2 /* Build configuration list for PBXNativeTarget "SDWebImage" */;
			buildPhases = (
				33783D69751B087D045FCF1FCA02E724 /* Headers */,
				090ABC49CEE6AE75BCDDAAED6457F183 /* Sources */,
				BE4B02A2D96469A6EA01B3EE819C960D /* Frameworks */,
				44B3C0D7DDF289331B7732E9D87126DB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				174B3FB9BF1793D59EBC8855921A0A04 /* PBXTargetDependency */,
			);
			name = SDWebImage;
			productName = SDWebImage;
			productReference = B0B214D775196BA7CA8E17E53048A493 /* SDWebImage.framework */;
			productType = "com.apple.product-type.framework";
		};
		49920C603CBCC3F0FE9B4F1BDDBA722E /* Pods-PhotoCCTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AA7F8A39C032E2956C34BBFA6E1693D7 /* Build configuration list for PBXNativeTarget "Pods-PhotoCCTests" */;
			buildPhases = (
				3838A062A6C2B3B674040149F8276915 /* Headers */,
				273F1C1DA2A4834E76C4CCF735E62FD0 /* Sources */,
				B9831A4622FE3CB482D52999536CCF95 /* Frameworks */,
				57AEA0ACFFE44834A612950E6A06EAA8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8965DD0F73C5C2A7E3E2C34A6C538ABC /* PBXTargetDependency */,
			);
			name = "Pods-PhotoCCTests";
			productName = Pods_PhotoCCTests;
			productReference = 47F9F962DA8803BBBBCEA1861D326882 /* Pods_PhotoCCTests.framework */;
			productType = "com.apple.product-type.framework";
		};
		8E67D09C7E721142B1193D5F91E756A3 /* Pods-PhotoCC-PhotoCCUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 38C72D5623C60D7313CC4B99AC8607D8 /* Build configuration list for PBXNativeTarget "Pods-PhotoCC-PhotoCCUITests" */;
			buildPhases = (
				24BC5362AFDB902E84C891319577C021 /* Headers */,
				249E3247FE4D5BE10CB4FAF0074F1AC1 /* Sources */,
				5B2821D975627C377EBC3184ECF39ACF /* Frameworks */,
				3D1314DDF962AD3DC0E5082F282C3914 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BCBD1FEC923D0DD730EEB35335DABA01 /* PBXTargetDependency */,
			);
			name = "Pods-PhotoCC-PhotoCCUITests";
			productName = Pods_PhotoCC_PhotoCCUITests;
			productReference = 064E6871DF90C494746CC59AD26B7626 /* Pods_PhotoCC_PhotoCCUITests.framework */;
			productType = "com.apple.product-type.framework";
		};
		94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03C1896413D2E0B0FC3601B400A27DF2 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */;
			buildPhases = (
				A7357BCC420DBA1DADDF69C0A50A3B74 /* Sources */,
				EE3E08086C8160F5E5AB5BDDA53EC3A0 /* Frameworks */,
				FF567B361A53C6E813CCD25436AB6BF7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SDWebImage-SDWebImage";
			productName = SDWebImage;
			productReference = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		BCB90ADD30A24F0E647E1B034A6C988C /* Pods-PhotoCC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5FDAB38409A66FD0B79ADBFCAC2A4F9B /* Build configuration list for PBXNativeTarget "Pods-PhotoCC" */;
			buildPhases = (
				918F044567ADE40D91A64D5F01125A6D /* Headers */,
				8CAC3352A2742C25AAE0BF309A04F197 /* Sources */,
				6D47D76EB22E023FBAE54D4BBC344987 /* Frameworks */,
				F7DD5642A7E6A8185CC9F84F94260472 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7AF8AE85498D50DF9207B7D2AD059C81 /* PBXTargetDependency */,
			);
			name = "Pods-PhotoCC";
			productName = Pods_PhotoCC;
			productReference = BADDA45F7BCD8396E0C59C6023494634 /* Pods_PhotoCC.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 6FF82B92CF7F69A3E84B137FD3DE2D51 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				BCB90ADD30A24F0E647E1B034A6C988C /* Pods-PhotoCC */,
				8E67D09C7E721142B1193D5F91E756A3 /* Pods-PhotoCC-PhotoCCUITests */,
				49920C603CBCC3F0FE9B4F1BDDBA722E /* Pods-PhotoCCTests */,
				3847153A6E5EEFB86565BA840768F429 /* SDWebImage */,
				94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3D1314DDF962AD3DC0E5082F282C3914 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		44B3C0D7DDF289331B7732E9D87126DB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				16D7DCB7CC985C33EEC41B371C029C84 /* SDWebImage.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		57AEA0ACFFE44834A612950E6A06EAA8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7DD5642A7E6A8185CC9F84F94260472 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FF567B361A53C6E813CCD25436AB6BF7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E2BE70620A43365FAFC0D7AA9B90DD1 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		090ABC49CEE6AE75BCDDAAED6457F183 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA3021DED76B9B182CC9195A60EB1209 /* NSBezierPath+SDRoundedCorners.m in Sources */,
				48916DE9521F627589300512ECC2D4A5 /* NSButton+WebCache.m in Sources */,
				416DA8B2997381F954DBA6E6A53DA4A2 /* NSData+ImageContentType.m in Sources */,
				88A23DF6F5638AC66C28C4102824E8B5 /* NSImage+Compatibility.m in Sources */,
				1708C1D28B421C4AD310426D1695CE77 /* SDAnimatedImage.m in Sources */,
				7A4EB9ED5D4E03170FFE61FCB299687B /* SDAnimatedImagePlayer.m in Sources */,
				5111A0A0934551CD2B9DDB1A1CA79FA7 /* SDAnimatedImageRep.m in Sources */,
				62FE895DF9D65A2955A275D909ECBE18 /* SDAnimatedImageView.m in Sources */,
				67178A8153B1A2F1D0D544B8093E23C5 /* SDAnimatedImageView+WebCache.m in Sources */,
				C93E972E75F84674690300123984EC43 /* SDAssociatedObject.m in Sources */,
				0B0E6CECDF516BC83756C1D5515A725B /* SDAsyncBlockOperation.m in Sources */,
				69A06A02F52EB26259FAD1DF6B121BE1 /* SDCallbackQueue.m in Sources */,
				526485EF6D2B62B24DB59122FB94BD42 /* SDDeviceHelper.m in Sources */,
				9CE425B89294BE2C13E70A86E75B15CF /* SDDiskCache.m in Sources */,
				5308E660E723C11E7691D311FD59C459 /* SDDisplayLink.m in Sources */,
				9345137ED10358B60E37D05FB6165759 /* SDFileAttributeHelper.m in Sources */,
				71F2B8CBB99087F348C472230200586F /* SDGraphicsImageRenderer.m in Sources */,
				ECE64B732F9FA7C402DDEEC58DCB9D98 /* SDImageAPNGCoder.m in Sources */,
				6B0978C9398336656EE309E62060AEAB /* SDImageAssetManager.m in Sources */,
				597E390C0BBB75B8045B651C487C2034 /* SDImageAWebPCoder.m in Sources */,
				0FF9F459ED16719292443A4C99B52B20 /* SDImageCache.m in Sources */,
				FCDEC6A53CF5517E1AF5B331FD65F6D9 /* SDImageCacheConfig.m in Sources */,
				FEA8BA4F82CCBD1D28DCC7EF39FB4096 /* SDImageCacheDefine.m in Sources */,
				33D3587AF629B2FA21554DA002D6ACB8 /* SDImageCachesManager.m in Sources */,
				55F7C7F055A18044497F8C88CAE34118 /* SDImageCachesManagerOperation.m in Sources */,
				E8AB529B9E0B4C23921344F6C4ABFEA4 /* SDImageCoder.m in Sources */,
				B2704AFFC5CC053154839DB44924D255 /* SDImageCoderHelper.m in Sources */,
				4D2C79AB2D24CFEC864F08D913CE7692 /* SDImageCodersManager.m in Sources */,
				24E8E4ED0B5D988E3346E6638619F4E4 /* SDImageFrame.m in Sources */,
				320DE42AF3CFE11FF785FEB1A7E6547B /* SDImageFramePool.m in Sources */,
				83530BF68848CD2C4A79A1FD69B304A5 /* SDImageGIFCoder.m in Sources */,
				E50613C67DD02AF6EA825DA0B31EFFAD /* SDImageGraphics.m in Sources */,
				18660FA595DBE133BB784E813A7122A8 /* SDImageHEICCoder.m in Sources */,
				288D796F3F7B9F42690E24A3B1018B2C /* SDImageIOAnimatedCoder.m in Sources */,
				717F76926C7BCB5B10C3037AD9239084 /* SDImageIOCoder.m in Sources */,
				6EFEEE3AE22E97DCEC4F5A3B88F56FC7 /* SDImageLoader.m in Sources */,
				85C0B4EE334B9972299E62DE61A4BB56 /* SDImageLoadersManager.m in Sources */,
				3D0BBFEC1921CE71BC240DC18D8BE540 /* SDImageTransformer.m in Sources */,
				864972FB0DF4B464B1B505AA5F788E91 /* SDInternalMacros.m in Sources */,
				E0BCF21E9FA59F638C13ECCECC4D9690 /* SDMemoryCache.m in Sources */,
				CA1E0DCDF679EA2DE2ED0915426E1D04 /* SDWeakProxy.m in Sources */,
				96E97174F4614FFA0649085022CB4AFE /* SDWebImage-dummy.m in Sources */,
				B95C63A039D9D08896421291DEBD3AEB /* SDWebImageCacheKeyFilter.m in Sources */,
				D06BB547D59D183FD1DDD84DEBAC9EE8 /* SDWebImageCacheSerializer.m in Sources */,
				1BC44E2FDD197D5210A23C9CCF1A906B /* SDWebImageCompat.m in Sources */,
				31DC2EC78AD1F8241AE6051EF9E73B0A /* SDWebImageDefine.m in Sources */,
				752822FE3F5092322D18FEC4533B79A9 /* SDWebImageDownloader.m in Sources */,
				74E069F8C9E22C0E37F261A5AB03A613 /* SDWebImageDownloaderConfig.m in Sources */,
				******************************** /* SDWebImageDownloaderDecryptor.m in Sources */,
				AA1EA8F0F0470F1596B1FFA58ABF3375 /* SDWebImageDownloaderOperation.m in Sources */,
				00DAE48C9A4FBCD1FCAA922CA57B45F9 /* SDWebImageDownloaderRequestModifier.m in Sources */,
				2F6D9BEA582A2DBB70A6C3B2FC2DB91E /* SDWebImageDownloaderResponseModifier.m in Sources */,
				425C9EA28FBEB7F7FC09A3F4A88C5955 /* SDWebImageError.m in Sources */,
				CFF8D1A5E4C2097EF05E1021FE112886 /* SDWebImageIndicator.m in Sources */,
				97235408E59E16C18B6BDA1D29E1CB26 /* SDWebImageManager.m in Sources */,
				1754DD5511A7BF462B116F70B0D4006A /* SDWebImageOperation.m in Sources */,
				906DCE66CD5BD236081D468616199BB7 /* SDWebImageOptionsProcessor.m in Sources */,
				0F1D0F5DCC8C94A4C684DF846D14F436 /* SDWebImagePrefetcher.m in Sources */,
				3187FF0C251D1B78BE87F64F6F6E944A /* SDWebImageTransition.m in Sources */,
				A92AB5E65CA85947368E46E6627F1BFB /* UIButton+WebCache.m in Sources */,
				6F3637EE643EABB1DE9212EA68649A64 /* UIColor+SDHexString.m in Sources */,
				E4F1B478580D6D7328BC29607BDE46F6 /* UIImage+ExtendedCacheData.m in Sources */,
				08D50C5AC969A3701B6F9137CF3A10F1 /* UIImage+ForceDecode.m in Sources */,
				C2840BF1950FF7EE2DCD6D55F768A49C /* UIImage+GIF.m in Sources */,
				596180E0EC9F46D12BA840DC4AA62659 /* UIImage+MemoryCacheCost.m in Sources */,
				694B8697854A776E32032999B2EF1FEA /* UIImage+Metadata.m in Sources */,
				BCDC1E1D46DD124B5726A064D2EE66A3 /* UIImage+MultiFormat.m in Sources */,
				06C4E233E7977DB81A24482E69B2D7D7 /* UIImage+Transform.m in Sources */,
				FCEE5BD645E95FF55468C4AB6D17CFDA /* UIImageView+HighlightedWebCache.m in Sources */,
				38938E604A7D708E6378A44063EF3512 /* UIImageView+WebCache.m in Sources */,
				616A8338C42FB01748DF1BDDA944858D /* UIView+WebCache.m in Sources */,
				97385A64CA020489951EF769392C6DCF /* UIView+WebCacheOperation.m in Sources */,
				74C474676C69A80BEC29B0F55FDF4D19 /* UIView+WebCacheState.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249E3247FE4D5BE10CB4FAF0074F1AC1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				99B47E42059D3444873CD97CA4CA64F0 /* Pods-PhotoCC-PhotoCCUITests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		273F1C1DA2A4834E76C4CCF735E62FD0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E515931C2ABD3074169C8C7D41FF0EBE /* Pods-PhotoCCTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8CAC3352A2742C25AAE0BF309A04F197 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				51439008EEB898B6A8C82CE1CD0BAF56 /* Pods-PhotoCC-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7357BCC420DBA1DADDF69C0A50A3B74 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		174B3FB9BF1793D59EBC8855921A0A04 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SDWebImage-SDWebImage";
			target = 94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */;
			targetProxy = 7E80BC1C5D2C8FB368C470B193F71DFB /* PBXContainerItemProxy */;
		};
		7AF8AE85498D50DF9207B7D2AD059C81 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SDWebImage;
			target = 3847153A6E5EEFB86565BA840768F429 /* SDWebImage */;
			targetProxy = FB538B0CC2E092CFD16909249C0483BA /* PBXContainerItemProxy */;
		};
		8965DD0F73C5C2A7E3E2C34A6C538ABC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-PhotoCC";
			target = BCB90ADD30A24F0E647E1B034A6C988C /* Pods-PhotoCC */;
			targetProxy = EFB80E67904031A50B7E33FF507C33F1 /* PBXContainerItemProxy */;
		};
		BCBD1FEC923D0DD730EEB35335DABA01 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SDWebImage;
			target = 3847153A6E5EEFB86565BA840768F429 /* SDWebImage */;
			targetProxy = D3B8730050C0D25D601AB2E669DC7A49 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0A75AB4F86AAE78D74133C90A8DF9B46 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 368B5D43CA52582F2F96F2A98FF35CD6 /* Pods-PhotoCC.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		11CADC31FE3C7D5C18D4DF2DB9ACD51D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 13BFDD71869A9E73B181F64BF551ECE1 /* Pods-PhotoCC-PhotoCCUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1FDD1C3DA4FE431264DCE16B5F78B29D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		28E50D61A7D3ED325206507B381944B7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 586C2139449D3D7569E45F055C6F7779 /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		31BECF3BB1BBB1DD47F8B10668BA07FD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CF79145918A07ADC8DD646FA2ABD3CD6 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13.5;
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		3866FE2F44D14A164D6F0E7AD51DDD9F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 48D7B693B7689703DFA2D0321D436F58 /* Pods-PhotoCC-PhotoCCUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCC-PhotoCCUITests/Pods-PhotoCC-PhotoCCUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		577F6A4F727EA263C46B63AB0799CC2F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 586C2139449D3D7569E45F055C6F7779 /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13.5;
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6B82EFE7AFC2A808B0D3F48470BFE3A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2AEBD03FB2AA1BE01665E9168FAE2803 /* Pods-PhotoCC.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCC/Pods-PhotoCC.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		922B16F71449D3C996993E08F0CC6F74 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B888D2602C43CA2D5887217FE74A179F /* Pods-PhotoCCTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B530DF95D4B8B398EF58BCEEC2AB800E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		CE479DFCF907EF81A68E381EB1E93657 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A99FF2F19EADABB75FAE98E08A996353 /* Pods-PhotoCCTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 13;
				MODULEMAP_FILE = "Target Support Files/Pods-PhotoCCTests/Pods-PhotoCCTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		FE2A0121F9BD204E551E7967D3336514 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CF79145918A07ADC8DD646FA2ABD3CD6 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		03C1896413D2E0B0FC3601B400A27DF2 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28E50D61A7D3ED325206507B381944B7 /* Debug */,
				FE2A0121F9BD204E551E7967D3336514 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2F9E94B5F79E365095CB33D3D3FCA6A2 /* Build configuration list for PBXNativeTarget "SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				577F6A4F727EA263C46B63AB0799CC2F /* Debug */,
				31BECF3BB1BBB1DD47F8B10668BA07FD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		38C72D5623C60D7313CC4B99AC8607D8 /* Build configuration list for PBXNativeTarget "Pods-PhotoCC-PhotoCCUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11CADC31FE3C7D5C18D4DF2DB9ACD51D /* Debug */,
				3866FE2F44D14A164D6F0E7AD51DDD9F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B530DF95D4B8B398EF58BCEEC2AB800E /* Debug */,
				1FDD1C3DA4FE431264DCE16B5F78B29D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5FDAB38409A66FD0B79ADBFCAC2A4F9B /* Build configuration list for PBXNativeTarget "Pods-PhotoCC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B82EFE7AFC2A808B0D3F48470BFE3A3 /* Debug */,
				0A75AB4F86AAE78D74133C90A8DF9B46 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AA7F8A39C032E2956C34BBFA6E1693D7 /* Build configuration list for PBXNativeTarget "Pods-PhotoCCTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE479DFCF907EF81A68E381EB1E93657 /* Debug */,
				922B16F71449D3C996993E08F0CC6F74 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
