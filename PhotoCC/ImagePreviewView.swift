//
//  ImagePreviewView.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

protocol ImagePreviewViewDelegate: AnyObject {
    func imagePreviewView(_ previewView: ImagePreviewView, didChangeZoom zoom: CGFloat)
}

class ImagePreviewView: NSView {
    
    // MARK: - Properties
    weak var delegate: ImagePreviewViewDelegate?
    
    private var scrollView: NSScrollView!
    private var imageView: NSImageView!
    private var currentImage: NSImage?
    private var currentZoom: CGFloat = 1.0
    private var currentRotation: CGFloat = 0.0
    
    // MARK: - Initialization
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        // 创建图片视图
        imageView = NSImageView()
        imageView.imageScaling = .scaleProportionallyUpOrDown
        imageView.imageAlignment = .alignCenter
        imageView.wantsLayer = true
        
        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.documentView = imageView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = true
        scrollView.autohidesScrollers = true
        scrollView.allowsMagnification = true
        scrollView.minMagnification = 0.1
        scrollView.maxMagnification = 5.0
        scrollView.magnification = 1.0
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        
        // 设置滚动视图代理
        scrollView.delegate = self
        
        addSubview(scrollView)
        
        setupConstraints()
        setupGestureRecognizers()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    private func setupGestureRecognizers() {
        // 添加双击手势识别器
        let doubleClickGesture = NSClickGestureRecognizer(target: self, action: #selector(handleDoubleClick(_:)))
        doubleClickGesture.numberOfClicksRequired = 2
        addGestureRecognizer(doubleClickGesture)
    }
    
    // MARK: - Public Methods
    
    func setImage(_ image: NSImage?) {
        currentImage = image
        imageView.image = image
        
        if let image = image {
            // 重置变换
            resetView()
            
            // 调整图片视图大小
            let imageSize = image.size
            imageView.frame = NSRect(origin: .zero, size: imageSize)
            
            // 居中显示
            centerImage()
        }
    }
    
    func setZoom(_ zoom: CGFloat) {
        currentZoom = zoom
        scrollView.magnification = zoom
        delegate?.imagePreviewView(self, didChangeZoom: zoom)
    }
    
    func rotateBy(_ angle: CGFloat) {
        currentRotation += angle
        applyTransform()
    }
    
    func resetView() {
        currentZoom = 1.0
        currentRotation = 0.0
        scrollView.magnification = 1.0
        applyTransform()
        centerImage()
        delegate?.imagePreviewView(self, didChangeZoom: currentZoom)
    }
    
    // MARK: - Private Methods
    
    private func applyTransform() {
        guard let image = currentImage else { return }
        
        // 创建变换
        var transform = AffineTransform.identity
        
        // 应用旋转
        if currentRotation != 0 {
            let radians = currentRotation * .pi / 180
            transform.rotate(byRadians: radians)
        }
        
        // 应用变换到图片视图
        imageView.frameRotation = currentRotation
        
        // 调整图片视图大小以适应旋转
        let rotatedSize = rotatedImageSize(image.size, rotation: currentRotation)
        imageView.frame = NSRect(origin: .zero, size: rotatedSize)
        
        centerImage()
    }
    
    private func rotatedImageSize(_ size: NSSize, rotation: CGFloat) -> NSSize {
        let radians = abs(rotation * .pi / 180)
        let sin = abs(sin(radians))
        let cos = abs(cos(radians))
        
        let width = size.width * cos + size.height * sin
        let height = size.width * sin + size.height * cos
        
        return NSSize(width: width, height: height)
    }
    
    private func centerImage() {
        guard let documentView = scrollView.documentView else { return }
        
        let scrollViewSize = scrollView.bounds.size
        let documentSize = documentView.frame.size
        
        let horizontalOffset = max(0, (documentSize.width - scrollViewSize.width) / 2)
        let verticalOffset = max(0, (documentSize.height - scrollViewSize.height) / 2)
        
        scrollView.contentView.scroll(to: NSPoint(x: horizontalOffset, y: verticalOffset))
    }
    
    // MARK: - Gesture Handlers
    
    @objc private func handleDoubleClick(_ gesture: NSClickGestureRecognizer) {
        if scrollView.magnification == 1.0 {
            // 放大到适合窗口大小
            setZoom(2.0)
        } else {
            // 重置到原始大小
            setZoom(1.0)
        }
    }
}

// MARK: - NSScrollViewDelegate

extension ImagePreviewView: NSScrollViewDelegate {
    
    func scrollViewDidEndLiveMagnify(_ scrollView: NSScrollView) {
        currentZoom = scrollView.magnification
        delegate?.imagePreviewView(self, didChangeZoom: currentZoom)
    }
}
