//
//  SessionViewController.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit
import Combine

class SessionViewController: NSViewController {
    
    // MARK: - Properties
    private let session: Session
    private var photoLoader: PhotoLoader!
    
    private var scrollView: NSScrollView!
    private var collectionView: NSCollectionView!
    private var loadingIndicator: NSProgressIndicator!
    private var statusLabel: NSTextField!
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(session: Session) {
        self.session = session
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        setupPhotoLoader()
        setupUI()
        setupBindings()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        loadPhotos()
    }
    
    // MARK: - Setup
    
    private func setupPhotoLoader() {
        photoLoader = PhotoLoader()
    }
    
    private func setupUI() {
        setupCollectionView()
        setupLoadingIndicator()
        setupStatusLabel()
        setupConstraints()
    }
    
    private func setupCollectionView() {
        // 创建集合视图
        collectionView = NSCollectionView()
        collectionView.backgroundColors = [NSColor.clear]
        collectionView.isSelectable = true
        collectionView.allowsMultipleSelection = false
        
        // 设置布局
        let layout = NSCollectionViewFlowLayout()
        layout.itemSize = NSSize(width: 200, height: 200)
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        layout.sectionInset = NSEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
        collectionView.collectionViewLayout = layout
        
        // 注册单元格
        collectionView.register(
            PhotoCollectionViewItem.self,
            forItemWithIdentifier: NSUserInterfaceItemIdentifier("PhotoItem")
        )
        
        // 设置数据源和代理
        collectionView.dataSource = self
        collectionView.delegate = self
        
        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.documentView = collectionView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
    }
    
    private func setupLoadingIndicator() {
        loadingIndicator = NSProgressIndicator()
        loadingIndicator.style = .spinning
        loadingIndicator.isDisplayedWhenStopped = false
        loadingIndicator.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(loadingIndicator)
    }
    
    private func setupStatusLabel() {
        statusLabel = NSTextField(labelWithString: "")
        statusLabel.font = NSFont.systemFont(ofSize: 14)
        statusLabel.textColor = NSColor.secondaryLabelColor
        statusLabel.alignment = .center
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        statusLabel.isHidden = true
        
        view.addSubview(statusLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图约束
            scrollView.topAnchor.constraint(equalTo: view.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // 加载指示器约束
            loadingIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            
            // 状态标签约束
            statusLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            statusLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            statusLabel.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -20)
        ])
    }
    
    private func setupBindings() {
        // 监听照片加载状态
        photoLoader.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading {
                    self?.loadingIndicator.startAnimation(nil)
                    self?.statusLabel.isHidden = true
                } else {
                    self?.loadingIndicator.stopAnimation(nil)
                    self?.updateStatusLabel()
                }
            }
            .store(in: &cancellables)
        
        // 监听照片数据变化
        photoLoader.$photos
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.collectionView.reloadData()
                self?.updateStatusLabel()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Loading
    
    private func loadPhotos() {
        guard let directoryURL = session.directoryURL else {
            showError("无法访问目录：\(session.directoryPath)")
            return
        }
        
        photoLoader.loadPhotosFromDirectory(directoryURL)
    }
    
    private func updateStatusLabel() {
        let photoCount = photoLoader.photos.count
        if photoCount == 0 {
            statusLabel.stringValue = "此文件夹中没有找到图片"
            statusLabel.isHidden = false
        } else {
            statusLabel.stringValue = "共 \(photoCount) 张图片"
            statusLabel.isHidden = false
        }
    }
    
    private func showError(_ message: String) {
        statusLabel.stringValue = message
        statusLabel.textColor = NSColor.systemRed
        statusLabel.isHidden = false
    }
}

// MARK: - NSCollectionViewDataSource

extension SessionViewController: NSCollectionViewDataSource {
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return photoLoader.photos.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(
            withIdentifier: NSUserInterfaceItemIdentifier("PhotoItem"),
            for: indexPath
        ) as! PhotoCollectionViewItem
        
        let photo = photoLoader.photos[indexPath.item]
        item.configure(with: photo)
        
        return item
    }
}

// MARK: - NSCollectionViewDelegate

extension SessionViewController: NSCollectionViewDelegate {
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        // 处理图片选择
        guard let indexPath = indexPaths.first else { return }
        let photo = photoLoader.photos[indexPath.item]
        
        // 这里可以添加图片预览功能
        print("选中图片: \(photo.name)")
    }
}
