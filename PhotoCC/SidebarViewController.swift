//
//  SidebarViewController.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit
import Combine

protocol SidebarViewControllerDelegate: AnyObject {
    func didSelectSession(_ session: Session)
    func didRequestNewSession()
}

class SidebarViewController: NSViewController {
    
    // MARK: - Properties
    weak var delegate: SidebarViewControllerDelegate?
    
    private var tableView: NSTableView!
    private var scrollView: NSScrollView!
    private var headerView: NSView!
    private var newSessionButton: NSButton!
    
    private var sessions: [Session] = []
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        setupUI()
        setupConstraints()
        setupBindings()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        loadSessions()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // 创建头部视图
        setupHeaderView()
        
        // 创建表格视图
        setupTableView()
    }
    
    private func setupHeaderView() {
        headerView = NSView()
        headerView.translatesAutoresizingMaskIntoConstraints = false
        
        // 标题标签
        let titleLabel = NSTextField(labelWithString: "会话历史")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 新建会话按钮
        newSessionButton = NSButton()
        newSessionButton.title = "新建会话"
        newSessionButton.bezelStyle = .rounded
        newSessionButton.target = self
        newSessionButton.action = #selector(newSessionButtonClicked)
        newSessionButton.translatesAutoresizingMaskIntoConstraints = false
        
        headerView.addSubview(titleLabel)
        headerView.addSubview(newSessionButton)
        
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 16),
            titleLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            
            newSessionButton.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -16),
            newSessionButton.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            newSessionButton.widthAnchor.constraint(equalToConstant: 80),
            newSessionButton.heightAnchor.constraint(equalToConstant: 24)
        ])
        
        view.addSubview(headerView)
    }
    
    private func setupTableView() {
        // 创建表格视图
        tableView = NSTableView()
        tableView.headerView = nil
        tableView.intercellSpacing = NSSize(width: 0, height: 1)
        tableView.backgroundColor = NSColor.clear
        tableView.selectionHighlightStyle = .regular
        tableView.allowsEmptySelection = true
        tableView.allowsMultipleSelection = false
        
        // 创建列
        let column = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("SessionColumn"))
        column.title = "Sessions"
        column.isEditable = false
        tableView.addTableColumn(column)
        
        // 设置数据源和代理
        tableView.dataSource = self
        tableView.delegate = self
        
        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.documentView = tableView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 头部视图约束
            headerView.topAnchor.constraint(equalTo: view.topAnchor),
            headerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            headerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            headerView.heightAnchor.constraint(equalToConstant: 50),
            
            // 滚动视图约束
            scrollView.topAnchor.constraint(equalTo: headerView.bottomAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupBindings() {
        // 监听会话管理器的变化
        SessionManager.shared.$sessions
            .receive(on: DispatchQueue.main)
            .sink { [weak self] sessions in
                self?.sessions = sessions
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Actions
    
    @objc private func newSessionButtonClicked() {
        delegate?.didRequestNewSession()
    }
    
    // MARK: - Data Loading
    
    private func loadSessions() {
        sessions = SessionManager.shared.sessions
        tableView.reloadData()
    }
}

// MARK: - NSTableViewDataSource

extension SidebarViewController: NSTableViewDataSource {
    
    func numberOfRows(in tableView: NSTableView) -> Int {
        return sessions.count
    }
}

// MARK: - NSTableViewDelegate

extension SidebarViewController: NSTableViewDelegate {
    
    func tableView(_ tableView: NSTableView, viewFor tableColumn: NSTableColumn?, row: Int) -> NSView? {
        let identifier = NSUserInterfaceItemIdentifier("SessionCellView")
        
        var cellView = tableView.makeView(withIdentifier: identifier, owner: self) as? SessionCellView
        
        if cellView == nil {
            cellView = SessionCellView()
            cellView?.identifier = identifier
        }
        
        if row < sessions.count {
            cellView?.configure(with: sessions[row])
        }
        
        return cellView
    }
    
    func tableView(_ tableView: NSTableView, heightOfRow row: Int) -> CGFloat {
        return 60
    }
    
    func tableViewSelectionDidChange(_ notification: Notification) {
        let selectedRow = tableView.selectedRow
        if selectedRow >= 0 && selectedRow < sessions.count {
            let session = sessions[selectedRow]
            delegate?.didSelectSession(session)
        }
    }
}
