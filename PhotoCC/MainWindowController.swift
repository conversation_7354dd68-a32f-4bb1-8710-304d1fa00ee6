//
//  MainWindowController.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

class MainWindowController: NSWindowController {
    
    // MARK: - Properties
    private var splitViewController: NSSplitViewController!
    private var sidebarViewController: SidebarViewController!
    private var mainViewController: MainViewController!
    
    // MARK: - Initialization
    
    override init(window: NSWindow?) {
        super.init(window: window)
        setupWindow()
    }
    
    convenience init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 1200, height: 800),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        self.init(window: window)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupWindow()
    }
    
    // MARK: - Window Setup
    
    private func setupWindow() {
        guard let window = window else { return }

        window.title = "PhotoCC"
        window.minSize = NSSize(width: 800, height: 600)
        window.center()

        // 设置窗口代理
        window.delegate = self

        setupSplitView()
    }
    
    private func setupSplitView() {
        // 创建分割视图控制器
        splitViewController = NSSplitViewController()
        splitViewController.splitView.isVertical = true
        splitViewController.splitView.dividerStyle = .thin

        // 创建侧边栏
        sidebarViewController = SidebarViewController()
        let sidebarItem = NSSplitViewItem(sidebarWithViewController: sidebarViewController)
        sidebarItem.minimumThickness = 200
        sidebarItem.maximumThickness = 400
        sidebarItem.preferredThicknessFraction = 0.25

        // 创建主视图
        mainViewController = MainViewController()
        let mainItem = NSSplitViewItem(viewController: mainViewController)
        mainItem.minimumThickness = 400

        // 添加到分割视图
        splitViewController.addSplitViewItem(sidebarItem)
        splitViewController.addSplitViewItem(mainItem)

        // 设置为窗口内容
        window?.contentViewController = splitViewController

        // 设置委托关系
        sidebarViewController.delegate = mainViewController
    }
    
    override func windowDidLoad() {
        super.windowDidLoad()
        
        // 窗口加载完成后的额外设置
        window?.makeFirstResponder(sidebarViewController.view)
    }
}

// MARK: - NSWindowDelegate

extension MainWindowController: NSWindowDelegate {
    
    func windowWillClose(_ notification: Notification) {
        // 窗口关闭前的清理工作
    }
    
    func windowDidBecomeMain(_ notification: Notification) {
        // 窗口成为主窗口时的处理
    }
    
    func windowDidResignMain(_ notification: Notification) {
        // 窗口失去主窗口状态时的处理
    }
}
