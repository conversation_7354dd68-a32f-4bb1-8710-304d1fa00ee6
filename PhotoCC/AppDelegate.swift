//
//  AppDelegate.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import Cocoa
import SwiftUI

@main
class AppDelegate: NSObject, NSApplicationDelegate {

    @IBOutlet var window: NSWindow!
    private var mainWindowController: MainWindowController?

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // 创建主窗口控制器
        mainWindowController = MainWindowController()
        mainWindowController?.showWindow(nil)

        // 隐藏XIB窗口
        window?.orderOut(nil)
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

