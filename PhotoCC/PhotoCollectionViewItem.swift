//
//  PhotoCollectionViewItem.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

class PhotoCollectionViewItem: NSCollectionViewItem {
    
    // MARK: - Properties
    private var thumbnailImageView: NSImageView!
    private var nameLabel: NSTextField!
    private var containerView: NSView!
    
    private var photoItem: PhotoItem?
    
    // MARK: - Initialization
    
    override func loadView() {
        view = NSView()
        setupUI()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupAppearance()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // 创建容器视图
        containerView = NSView()
        containerView.wantsLayer = true
        containerView.layer?.cornerRadius = 8
        containerView.layer?.borderWidth = 1
        containerView.layer?.borderColor = NSColor.separatorColor.cgColor
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建缩略图视图
        thumbnailImageView = NSImageView()
        thumbnailImageView.imageScaling = .scaleProportionallyUpOrDown
        thumbnailImageView.imageAlignment = .alignCenter
        thumbnailImageView.wantsLayer = true
        thumbnailImageView.layer?.cornerRadius = 6
        thumbnailImageView.layer?.masksToBounds = true
        thumbnailImageView.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建名称标签
        nameLabel = NSTextField()
        nameLabel.isEditable = false
        nameLabel.isBordered = false
        nameLabel.backgroundColor = NSColor.clear
        nameLabel.font = NSFont.systemFont(ofSize: 12)
        nameLabel.textColor = NSColor.labelColor
        nameLabel.alignment = .center
        nameLabel.lineBreakMode = .byTruncatingMiddle
        nameLabel.maximumNumberOfLines = 2
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加子视图
        containerView.addSubview(thumbnailImageView)
        containerView.addSubview(nameLabel)
        view.addSubview(containerView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 容器视图约束
            containerView.topAnchor.constraint(equalTo: view.topAnchor, constant: 4),
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 4),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -4),
            containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -4),
            
            // 缩略图视图约束
            thumbnailImageView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            thumbnailImageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 8),
            thumbnailImageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            thumbnailImageView.heightAnchor.constraint(equalTo: thumbnailImageView.widthAnchor),
            
            // 名称标签约束
            nameLabel.topAnchor.constraint(equalTo: thumbnailImageView.bottomAnchor, constant: 4),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            nameLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -8)
        ])
    }
    
    private func setupAppearance() {
        // 设置默认外观
        updateSelectionAppearance()
    }
    
    // MARK: - Configuration
    
    func configure(with photoItem: PhotoItem) {
        self.photoItem = photoItem
        
        // 设置名称
        nameLabel.stringValue = photoItem.name
        
        // 异步加载缩略图
        loadThumbnail(for: photoItem)
    }
    
    private func loadThumbnail(for photoItem: PhotoItem) {
        // 先显示占位图
        thumbnailImageView.image = NSImage(systemSymbolName: "photo", accessibilityDescription: nil)
        
        // 异步加载缩略图
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let thumbnail = photoItem.thumbnail
            
            DispatchQueue.main.async {
                guard self?.photoItem?.id == photoItem.id else { return }
                self?.thumbnailImageView.image = thumbnail
            }
        }
    }
    
    // MARK: - Selection Handling
    
    override var isSelected: Bool {
        didSet {
            updateSelectionAppearance()
        }
    }
    
    private func updateSelectionAppearance() {
        if isSelected {
            containerView.layer?.borderColor = NSColor.controlAccentColor.cgColor
            containerView.layer?.borderWidth = 2
            containerView.layer?.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.1).cgColor
        } else {
            containerView.layer?.borderColor = NSColor.separatorColor.cgColor
            containerView.layer?.borderWidth = 1
            containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }
    }
    
    // MARK: - Mouse Events
    
    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)
        
        if !isSelected {
            containerView.layer?.borderColor = NSColor.controlAccentColor.withAlphaComponent(0.5).cgColor
            containerView.layer?.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.05).cgColor
        }
    }
    
    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)
        
        if !isSelected {
            containerView.layer?.borderColor = NSColor.separatorColor.cgColor
            containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        
        // 移除旧的跟踪区域
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }
        
        // 添加新的跟踪区域
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
}
