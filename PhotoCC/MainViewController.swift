//
//  MainViewController.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

class MainViewController: NSViewController {

    // MARK: - Properties
    private var tabView: NSTabView!
    private var emptyStateView: NSView!
    private var emptyStateLabel: NSTextField!

    private var sessionTabs: [Session.ID: NSTabViewItem] = [:]
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        setupUI()
        setupBindings()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        showEmptyState()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        setupTabView()
        setupEmptyState()
    }
    
    private func setupTabView() {
        tabView = NSTabView()
        tabView.tabViewType = .topTabsBezelBorder
        tabView.delegate = self
        tabView.translatesAutoresizingMaskIntoConstraints = false
        tabView.isHidden = true // 初始隐藏
        
        view.addSubview(tabView)
        
        NSLayoutConstraint.activate([
            tabView.topAnchor.constraint(equalTo: view.topAnchor),
            tabView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tabView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tabView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupEmptyState() {
        emptyStateView = NSView()
        emptyStateView.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建空状态图标
        let iconImageView = NSImageView()
        iconImageView.image = NSImage(systemSymbolName: "photo.on.rectangle.angled", accessibilityDescription: nil)
        iconImageView.contentTintColor = NSColor.tertiaryLabelColor
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建空状态标签
        emptyStateLabel = NSTextField(labelWithString: "选择一个会话开始浏览照片")
        emptyStateLabel.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        emptyStateLabel.textColor = NSColor.tertiaryLabelColor
        emptyStateLabel.alignment = .center
        emptyStateLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建提示标签
        let hintLabel = NSTextField(labelWithString: "从左侧选择一个会话，或点击「新建会话」开始")
        hintLabel.font = NSFont.systemFont(ofSize: 14)
        hintLabel.textColor = NSColor.quaternaryLabelColor
        hintLabel.alignment = .center
        hintLabel.translatesAutoresizingMaskIntoConstraints = false
        
        emptyStateView.addSubview(iconImageView)
        emptyStateView.addSubview(emptyStateLabel)
        emptyStateView.addSubview(hintLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            iconImageView.centerYAnchor.constraint(equalTo: emptyStateView.centerYAnchor, constant: -40),
            iconImageView.widthAnchor.constraint(equalToConstant: 64),
            iconImageView.heightAnchor.constraint(equalToConstant: 64),
            
            emptyStateLabel.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            emptyStateLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 16),
            
            hintLabel.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            hintLabel.topAnchor.constraint(equalTo: emptyStateLabel.bottomAnchor, constant: 8)
        ])
        
        view.addSubview(emptyStateView)
        
        NSLayoutConstraint.activate([
            emptyStateView.topAnchor.constraint(equalTo: view.topAnchor),
            emptyStateView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            emptyStateView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            emptyStateView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupBindings() {
        // 监听当前会话变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(currentSessionDidChange),
            name: .currentSessionDidChange,
            object: nil
        )
    }

    @objc private func currentSessionDidChange() {
        DispatchQueue.main.async { [weak self] in
            if let session = SessionManager.shared.currentSession {
                self?.openSessionTab(session)
            } else {
                self?.showEmptyState()
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Tab Management
    
    private func openSessionTab(_ session: Session) {
        hideEmptyState()
        
        // 检查是否已经有这个会话的标签页
        if let existingTab = sessionTabs[session.id] {
            tabView.selectTabViewItem(existingTab)
            return
        }
        
        // 创建新的标签页
        let sessionViewController = SessionViewController(session: session)
        let tabItem = NSTabViewItem(viewController: sessionViewController)
        tabItem.label = session.name
        tabItem.identifier = session.id
        
        // 添加到标签视图
        tabView.addTabViewItem(tabItem)
        tabView.selectTabViewItem(tabItem)
        
        // 记录标签页
        sessionTabs[session.id] = tabItem
    }
    
    private func closeSessionTab(_ session: Session) {
        guard let tabItem = sessionTabs[session.id] else { return }
        
        tabView.removeTabViewItem(tabItem)
        sessionTabs.removeValue(forKey: session.id)
        
        // 如果没有标签页了，显示空状态
        if sessionTabs.isEmpty {
            showEmptyState()
        }
    }
    
    private func showEmptyState() {
        tabView.isHidden = true
        emptyStateView.isHidden = false
    }
    
    private func hideEmptyState() {
        emptyStateView.isHidden = true
        tabView.isHidden = false
    }
    
    // MARK: - Public Methods
    
    func openDirectoryPicker() {
        let openPanel = NSOpenPanel()
        openPanel.canChooseFiles = false
        openPanel.canChooseDirectories = true
        openPanel.allowsMultipleSelection = false
        openPanel.prompt = "选择图片文件夹"
        
        openPanel.begin { [weak self] response in
            if response == .OK, let url = openPanel.url {
                // 创建新会话
                if let session = SessionManager.shared.createSessionWithBookmark(from: url) {
                    SessionManager.shared.openSession(session)
                }
            }
        }
    }
}

// MARK: - SidebarViewControllerDelegate

extension MainViewController: SidebarViewControllerDelegate {
    
    func didSelectSession(_ session: Session) {
        SessionManager.shared.openSession(session)
    }
    
    func didRequestNewSession() {
        openDirectoryPicker()
    }
}

// MARK: - NSTabViewDelegate

extension MainViewController: NSTabViewDelegate {
    
    func tabView(_ tabView: NSTabView, didSelect tabViewItem: NSTabViewItem?) {
        // 标签页选择变化时的处理
        if let identifier = tabViewItem?.identifier as? Session.ID {
            // 可以在这里更新当前会话状态
        }
    }
    
    func tabView(_ tabView: NSTabView, shouldSelect tabViewItem: NSTabViewItem?) -> Bool {
        return true
    }
}
