//
//  TabBarView.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import SwiftUI

// MARK: - Tab栏视图
struct TabBarView: View {
    let tabs: [Session]
    @Binding var selectedTabId: UUID?
    let onCloseTab: (Session) -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(tabs) { session in
                    TabItemView(
                        session: session,
                        isSelected: selectedTabId == session.id,
                        onSelect: {
                            selectedTabId = session.id
                        },
                        onClose: {
                            onCloseTab(session)
                        }
                    )
                }
            }
            .padding(.horizontal, 8)
        }
        .frame(height: 36)
        .background(Color(NSColor.controlBackgroundColor))
    }
}

// MARK: - Tab项视图
struct TabItemView: View {
    let session: Session
    let isSelected: Bool
    let onSelect: () -> Void
    let onClose: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 6) {
            // 文件夹图标
            Image(systemName: session.hasValidBookmarkData ? "folder.fill" : "folder")
                .font(.system(size: 12))
                .foregroundColor(session.hasValidBookmarkData ? .blue : .secondary)
            
            // 会话名称
            Text(session.name)
                .font(.system(size: 12, weight: isSelected ? .medium : .regular))
                .foregroundColor(isSelected ? .primary : .secondary)
                .lineLimit(1)
            
            // 关闭按钮
            if isHovered || isSelected {
                Button(action: onClose) {
                    Image(systemName: "xmark")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .frame(width: 14, height: 14)
                .background(
                    Circle()
                        .fill(Color.secondary.opacity(0.2))
                )
                .onHover { hovering in
                    // 处理关闭按钮的悬停状态
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(isSelected ? Color(NSColor.selectedControlColor) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(
                    isSelected ? Color.accentColor : Color.clear,
                    lineWidth: isSelected ? 1 : 0
                )
        )
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.1)) {
                isHovered = hovering
            }
        }
        .contextMenu {
            Button("关闭标签页") {
                onClose()
            }
            
            Button("关闭其他标签页") {
                // 这里需要通过回调来实现
            }
            
            Divider()
            
            Button("在Finder中显示") {
                if let url = session.directoryURL {
                    NSWorkspace.shared.selectFile(nil, inFileViewerRootedAtPath: url.path)
                }
            }
        }
    }
}

// MARK: - Tab内容视图
struct TabContentView: View {
    let tabs: [Session]
    let selectedTabId: UUID?
    
    var selectedSession: Session? {
        tabs.first { $0.id == selectedTabId }
    }
    
    var body: some View {
        Group {
            if let session = selectedSession {
                SessionContentView(session: session)
            } else {
                // 没有选中的tab时显示空状态
                VStack {
                    Image(systemName: "photo")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    Text("请选择一个会话")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            }
        }
    }
}

// MARK: - 会话内容视图（占位）
struct SessionContentView: View {
    let session: Session
    @StateObject private var photoLoader = PhotoLoader()
    
    var body: some View {
        VStack {
            // 顶部信息栏
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(session.name)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(session.directoryPath)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    if photoLoader.isLoading {
                        HStack(spacing: 6) {
                            ProgressView()
                                .scaleEffect(0.7)
                            Text("正在加载图片...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Text("共 \(photoLoader.photos.count) 张图片")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 刷新按钮
                Button(action: {
                    loadPhotos()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 14))
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(photoLoader.isLoading)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // 图片内容区域（占位）
            if photoLoader.photos.isEmpty && !photoLoader.isLoading {
                VStack {
                    Image(systemName: "photo.on.rectangle")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    Text("此文件夹中没有图片")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    Text("支持的格式：JPG, PNG, GIF, HEIC 等")
                        .font(.caption)
                        .foregroundColor(.tertiary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 这里将来放置图片网格视图
                PhotoGridPlaceholderView(photos: photoLoader.photos)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.textBackgroundColor))
        .onAppear {
            loadPhotos()
        }
    }
    
    private func loadPhotos() {
        guard let url = session.directoryURL else {
            print("无法获取目录URL")
            return
        }
        
        photoLoader.loadPhotosFromDirectory(url)
    }
}

// MARK: - 图片网格占位视图
struct PhotoGridPlaceholderView: View {
    let photos: [PhotoItem]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 4), spacing: 8) {
                ForEach(photos) { photo in
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(1, contentMode: .fit)
                        .overlay(
                            VStack {
                                Image(systemName: "photo")
                                    .font(.system(size: 24))
                                    .foregroundColor(.secondary)
                                Text(photo.name)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        )
                }
            }
            .padding(16)
        }
    }
}

#Preview {
    VStack {
        TabBarView(
            tabs: [
                Session(name: "测试会话1", directoryPath: "/Users/<USER>/Pictures"),
                Session(name: "测试会话2", directoryPath: "/Users/<USER>/Documents")
            ],
            selectedTabId: .constant(UUID()),
            onCloseTab: { _ in }
        )
        
        Divider()
        
        SessionContentView(session: Session(name: "测试会话", directoryPath: "/Users/<USER>/Pictures"))
    }
    .frame(width: 800, height: 600)
}
