//
//  MainView.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import SwiftUI

struct MainView: View {
    @StateObject private var sessionManager = SessionManager.shared
    @State private var showSidebar = true
    @State private var selectedSessionId: UUID?
    @State private var openTabs: [Session] = []
    
    var body: some View {
        NavigationSplitView {
            // 左侧边栏
            SidebarView(
                sessions: sessionManager.sessions,
                selectedSessionId: $selectedSessionId,
                onSessionSelected: { session in
                    openSessionInTab(session)
                }
            )
            .navigationSplitViewColumnWidth(min: 200, ideal: 250, max: 300)
        } detail: {
            // 右侧主视图区域
            MainContentView(
                openTabs: $openTabs,
                selectedSessionId: $selectedSessionId,
                onCloseTab: { session in
                    closeTab(session)
                }
            )
        }
        .navigationSplitViewStyle(.balanced)
        .onReceive(NotificationCenter.default.publisher(for: .toggleSidebar)) { _ in
            withAnimation(.easeInOut(duration: 0.2)) {
                showSidebar.toggle()
            }
        }
        .onAppear {
            // 如果有当前会话，自动打开
            if let currentSession = sessionManager.currentSession {
                openSessionInTab(currentSession)
            }
        }
    }
    
    private func openSessionInTab(_ session: Session) {
        // 检查是否已经打开
        if !openTabs.contains(where: { $0.id == session.id }) {
            openTabs.append(session)
        }
        selectedSessionId = session.id
        sessionManager.openSession(session)
    }
    
    private func closeTab(_ session: Session) {
        openTabs.removeAll { $0.id == session.id }
        
        // 如果关闭的是当前选中的tab，选择其他tab
        if selectedSessionId == session.id {
            selectedSessionId = openTabs.first?.id
        }
    }
}

// MARK: - 侧边栏视图
struct SidebarView: View {
    let sessions: [Session]
    @Binding var selectedSessionId: UUID?
    let onSessionSelected: (Session) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 标题
            HStack {
                Text("历史会话")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // 会话列表
            if sessions.isEmpty {
                VStack {
                    Spacer()
                    Image(systemName: "photo.on.rectangle")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    Text("暂无会话")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    Text("点击工具栏的"打开文件夹"按钮\n开始浏览图片")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    Spacer()
                }
                .frame(maxWidth: .infinity)
            } else {
                ScrollView {
                    LazyVStack(spacing: 1) {
                        ForEach(sessions) { session in
                            SessionRowView(
                                session: session,
                                isSelected: selectedSessionId == session.id,
                                onTap: {
                                    onSessionSelected(session)
                                }
                            )
                        }
                    }
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor))
    }
}

// MARK: - 会话行视图
struct SessionRowView: View {
    let session: Session
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 文件夹图标
            Image(systemName: session.hasValidBookmarkData ? "folder.fill" : "folder")
                .font(.system(size: 16))
                .foregroundColor(session.hasValidBookmarkData ? .blue : .secondary)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(session.name)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(session.directoryPath)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                Text(session.formattedLastAccessedAt)
                    .font(.system(size: 10))
                    .foregroundColor(.tertiary)
            }
            
            Spacer()
            
            // 状态指示器
            if !session.isDirectoryValid {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.orange)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            Rectangle()
                .fill(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        )
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
        .contextMenu {
            Button("移除会话") {
                SessionManager.shared.removeSession(session)
            }
        }
    }
}

// MARK: - 主内容视图
struct MainContentView: View {
    @Binding var openTabs: [Session]
    @Binding var selectedSessionId: UUID?
    let onCloseTab: (Session) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            if openTabs.isEmpty {
                // 空状态
                EmptyStateView()
            } else {
                // Tab栏
                TabBarView(
                    tabs: openTabs,
                    selectedTabId: $selectedSessionId,
                    onCloseTab: onCloseTab
                )
                
                Divider()
                
                // Tab内容
                TabContentView(
                    tabs: openTabs,
                    selectedTabId: selectedSessionId
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 空状态视图
struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.stack")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            Text("欢迎使用 PhotoCC")
                .font(.title)
                .foregroundColor(.primary)
            
            Text("选择一个包含图片的文件夹开始浏览")
                .font(.body)
                .foregroundColor(.secondary)
            
            Button("选择文件夹") {
                if let url = SessionManager.shared.selectDirectory() {
                    _ = SessionManager.shared.createSessionWithBookmark(from: url)
                }
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.textBackgroundColor))
    }
}

#Preview {
    MainView()
        .frame(width: 1200, height: 800)
}
